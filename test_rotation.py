#!/usr/bin/env python3
"""
Script de test pour vérifier la rotation des images
"""
import os
from PIL import Image, ImageDraw, ImageFont
import tempfile

def create_test_image():
    """Crée une image de test avec du texte pour vérifier la rotation"""
    # Créer une image de test 200x200 avec du texte
    img = Image.new('RGB', (200, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Dessiner un rectangle et du texte pour voir l'orientation
    draw.rectangle([10, 10, 190, 50], fill='blue')
    draw.text((20, 20), "TOP", fill='white')
    draw.text((20, 160), "BOTTOM", fill='black')
    draw.text((10, 100), "LEFT", fill='red')
    draw.text((150, 100), "RIGHT", fill='green')
    
    # Dessiner une flèche pointant vers le haut
    draw.polygon([(100, 60), (90, 80), (110, 80)], fill='red')  # Flèche vers le haut
    
    return img

def test_rotation():
    """Test la rotation avec PIL"""
    print("🧪 Test de rotation avec PIL...")
    
    # Créer un dossier temporaire
    with tempfile.TemporaryDirectory() as temp_dir:
        test_image_path = os.path.join(temp_dir, "test_original.png")
        
        # Créer et sauvegarder l'image de test
        img = create_test_image()
        img.save(test_image_path)
        print(f"✅ Image de test créée: {test_image_path}")
        
        # Test des rotations
        angles = [90, 180, 270]
        
        for angle in angles:
            print(f"\n🔄 Test rotation {angle}°...")
            
            # Charger l'image originale
            with Image.open(test_image_path) as original:
                # Rotation PIL (sens anti-horaire par défaut)
                rotated = original.rotate(-angle, expand=True)  # Négatif pour rotation horaire
                
                # Sauvegarder le résultat
                result_path = os.path.join(temp_dir, f"test_rotated_{angle}.png")
                rotated.save(result_path)
                print(f"✅ Image pivotée sauvegardée: {result_path}")
                
                # Vérifier les dimensions
                print(f"   Original: {original.size}")
                print(f"   Pivoté: {rotated.size}")
        
        print(f"\n📂 Vérifiez les images dans: {temp_dir}")
        input("Appuyez sur Entrée pour continuer (les fichiers temporaires seront supprimés)...")

if __name__ == "__main__":
    test_rotation()
