"""
Interface graphique pour utiliser les scripts de traitement d'images
Auteur: Gabriel <PERSON>
Date: 2025-06-18
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import subprocess
import sys
from pathlib import Path
import threading
import json
import queue

class LogWindow:
    """Fenêtre séparée pour afficher les logs en temps réel"""

    def __init__(self, parent, title="Logs en temps réel", script_type="Script"):
        self.parent = parent
        self.script_type = script_type

        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(f"{title} - {script_type}")
        self.window.geometry("900x600")

        # Queue pour les messages
        self.log_queue = queue.Queue()

        # Variables de contrôle
        self.process = None
        self.is_running = False

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface de la fenêtre de logs"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre et informations
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(title_frame, text=f"📊 {self.script_type} - Logs en temps réel",
                 font=('Arial', 14, 'bold')).pack(side=tk.LEFT)

        # Bouton pour arrêter le processus
        self.stop_btn = ttk.Button(title_frame, text="⏹️ Arrêter",
                                  command=self.stop_process, state='disabled')
        self.stop_btn.pack(side=tk.RIGHT)

        # Zone de logs
        log_frame = ttk.LabelFrame(main_frame, text="Logs", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # Zone de texte avec scrollbar
        self.log_text = scrolledtext.ScrolledText(log_frame, height=30, width=100,
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Frame pour les boutons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # Boutons de contrôle
        ttk.Button(button_frame, text="🗑️ Effacer logs",
                  command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="💾 Sauvegarder logs",
                  command=self.save_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ Fermer",
                  command=self.close_window).pack(side=tk.RIGHT)

        # Démarrer la vérification de la queue
        self.check_queue()

    def add_log(self, message):
        """Ajoute un message à la queue des logs"""
        self.log_queue.put(message)

    def check_queue(self):
        """Vérifie la queue des messages et met à jour l'affichage"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_text.insert(tk.END, message)
                self.log_text.see(tk.END)
                self.log_text.update_idletasks()
        except queue.Empty:
            pass

        # Programmer la prochaine vérification
        self.window.after(100, self.check_queue)

    def start_process(self, script_path, args=None):
        """Démarre le processus et capture sa sortie"""
        if self.is_running:
            return

        self.is_running = True
        self.stop_btn.config(state='normal')

        # Démarrer le processus dans un thread séparé
        thread = threading.Thread(target=self._run_process, args=(script_path, args))
        thread.daemon = True
        thread.start()

    def _run_process(self, script_path, args=None):
        """Exécute le processus et capture sa sortie"""
        cmd = [sys.executable, script_path]
        if args:
            cmd.extend(args)

        try:
            script_dir = os.path.dirname(os.path.abspath(script_path))
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            self.add_log(f"🚀 Démarrage: {' '.join(cmd)}\n")
            self.add_log(f"📂 Répertoire: {script_dir}\n")
            self.add_log("=" * 80 + "\n\n")

            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=script_dir,
                env=env,
                encoding='utf-8',
                errors='replace',
                bufsize=1,
                universal_newlines=True
            )

            # Lire la sortie ligne par ligne
            for line in iter(self.process.stdout.readline, ''):
                if not self.is_running:
                    break
                self.add_log(line)

            self.process.wait()

            if self.process.returncode == 0:
                self.add_log(f"\n\n✅ {self.script_type} terminé avec succès!\n")
            else:
                self.add_log(f"\n\n❌ {self.script_type} terminé avec erreur (code: {self.process.returncode})\n")

        except Exception as e:
            self.add_log(f"\n\n💥 Erreur lors de l'exécution: {str(e)}\n")
        finally:
            self.is_running = False
            self.window.after_idle(lambda: self.stop_btn.config(state='disabled'))

    def stop_process(self):
        """Arrête le processus en cours"""
        if self.process and self.is_running:
            try:
                self.process.terminate()
                self.add_log(f"\n\n⏹️ {self.script_type} arrêté par l'utilisateur\n")
            except Exception as e:
                self.add_log(f"\n\n❌ Erreur lors de l'arrêt: {str(e)}\n")
        self.is_running = False
        self.stop_btn.config(state='disabled')

    def clear_logs(self):
        """Efface tous les logs"""
        self.log_text.delete(1.0, tk.END)

    def save_logs(self):
        """Sauvegarde les logs dans un fichier"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Fichiers texte", "*.txt"), ("Tous les fichiers", "*.*")],
                title="Sauvegarder les logs"
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.add_log(f"\n💾 Logs sauvegardés dans: {filename}\n")
        except Exception as e:
            self.add_log(f"\n❌ Erreur lors de la sauvegarde: {str(e)}\n")

    def close_window(self):
        """Ferme la fenêtre"""
        if self.is_running:
            self.stop_process()
        self.window.destroy()

class ScriptGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Interface de Traitement d'Images")
        self.root.geometry("900x700")

        # Fichier de configuration pour mémoriser les chemins
        self.config_file = "interface_paths_memory.json"
        self.path_memory = self.load_path_memory()

        # Variables pour stocker les chemins
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.labels_path = tk.StringVar()
        self.file_path = tk.StringVar()



        # Variables pour autres scripts
        self.dataset_name = tk.StringVar()
        self.slices_per_volume = tk.StringVar()
        self.max_images = tk.StringVar()

        # Variables pour les exporteurs PNG
        self.export_prefix = tk.StringVar()
        self.export_dpi = tk.StringVar()
        self.export_image_type = tk.StringVar()
        self.export_start_slice = tk.StringVar()
        self.export_end_slice = tk.StringVar()
        self.export_uint8_mode = tk.BooleanVar()
        self.h5_colorize_mode = tk.StringVar()

        self.setup_ui()

    def load_path_memory(self):
        """Charge la mémoire des chemins depuis le fichier de configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Erreur lors du chargement de la mémoire des chemins: {e}")
        return {}

    def save_path_memory(self):
        """Sauvegarde la mémoire des chemins dans le fichier de configuration"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.path_memory, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Erreur lors de la sauvegarde de la mémoire des chemins: {e}")

    def get_remembered_path(self, script_name, path_type):
        """Récupère le chemin mémorisé pour un script et un type de chemin donnés"""
        return self.path_memory.get(script_name, {}).get(path_type, "")

    def remember_path(self, script_name, path_type, path):
        """Mémorise un chemin pour un script et un type de chemin donnés"""
        if script_name not in self.path_memory:
            self.path_memory[script_name] = {}
        self.path_memory[script_name][path_type] = path
        self.save_path_memory()

    def setup_ui(self):
        # Style
        style = ttk.Style()
        style.theme_use('clam')

        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Titre
        title_label = ttk.Label(main_frame, text="🔬 Interface de Traitement d'Images",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # Sélection du script
        ttk.Label(main_frame, text="Choisir le script:", font=('Arial', 12, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=5)

        self.script_var = tk.StringVar()
        self.script_combo = ttk.Combobox(main_frame, textvariable=self.script_var, width=50, state="readonly")
        self.script_combo['values'] = [
            "nifti_check_shape.py",
            "npz_check_shape.py",
            "h5_check_shape.py",
            "conversion_dataset_2d_to_3d.py",
            "conversion_inference_2d_to_3d.py",
            "nifti_transpose.py",
            "nifti_view_volume.py",
            "nifti_export_png.py",
            "nifti_export_dossier_png.py",
            "npz_view_volume.py",
            "npz_export_png.py",
            "npz_export_dossier_png.py",
            "h5_view_volume.py",
            "h5_export_png.py",
            "h5_export_dossier_png.py",
            "pngs_to_volume.py",
            "view_png_folder.py",
            "view_labels.py",
            "view_mask.py",
            "nde_viewer.py",
            "copy_random_images.py",
            "copy_matching_files.py",
            "copy_by_resolution.py",
            "util_directory_tree.py",
            "overlay.py",
            "H5py_NdeToImages.py",
            "H5py_compareFile.py",
            "H5py_enlever_dossier_inutile.py",
            "H5py_inversion.py",
            "H5py_openFile.py",
            "H5py_process_nde.py",
        ]
        self.script_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.script_combo.bind('<<ComboboxSelected>>', self.on_script_change)

        # Frame pour les paramètres
        self.params_frame = ttk.LabelFrame(main_frame, text="Paramètres", padding="10")
        self.params_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        self.params_frame.columnconfigure(1, weight=1)

        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=3, pady=10)

        # Bouton exécuter
        self.execute_btn = ttk.Button(buttons_frame, text="🚀 Exécuter", command=self.execute_script,
                                     style='Accent.TButton')
        self.execute_btn.pack(side=tk.LEFT, padx=5)

        # Bouton aide
        help_btn = ttk.Button(buttons_frame, text="❓ Aide", command=self.show_help)
        help_btn.pack(side=tk.LEFT, padx=5)

        # Zone de sortie
        output_frame = ttk.LabelFrame(main_frame, text="Sortie du script", padding="5")
        output_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)

        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=80)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du redimensionnement
        main_frame.rowconfigure(4, weight=1)

    def clear_params_frame(self):
        """Efface tous les widgets du frame des paramètres"""
        for widget in self.params_frame.winfo_children():
            widget.destroy()

    def on_script_change(self, event=None):
        """Appelé quand l'utilisateur change de script"""
        self.clear_params_frame()
        script_name = self.script_var.get()

        if "nifti_check_shape" in script_name:
            self.setup_check_shape_params()
        elif "npz_check_shape" in script_name:
            self.setup_npz_check_shape_params()
        elif "h5_check_shape" in script_name:
            self.setup_h5_check_shape_params()
        elif "conversion_dataset_2d_to_3d" in script_name:
            self.setup_dataset_2d_to_3d_params()
        elif "conversion_inference_2d_to_3d" in script_name:
            self.setup_inference_2d_to_3d_params()
        elif "nifti_transpose" in script_name:
            self.setup_transpose_params()
        elif "nifti_view_volume" in script_name:
            self.setup_view_volume_params()
        elif "nifti_export_png" in script_name and "dossier" not in script_name:
            self.setup_nifti_export_png_params()
        elif "nifti_export_dossier_png" in script_name:
            self.setup_nifti_export_dossier_png_params()
        elif "npz_view_volume" in script_name:
            self.setup_npz_view_volume_params()
        elif "npz_export_png" in script_name and "dossier" not in script_name:
            self.setup_npz_export_png_params()
        elif "npz_export_dossier_png" in script_name:
            self.setup_npz_export_dossier_png_params()
        elif "h5_view_volume" in script_name:
            self.setup_h5_view_volume_params()
        elif "h5_export_png" in script_name and "dossier" not in script_name:
            self.setup_h5_export_png_params()
        elif "h5_export_dossier_png" in script_name:
            self.setup_h5_export_dossier_png_params()
        elif "pngs_to_volume" in script_name:
            self.setup_pngs_to_volume_params()
        elif "view_png_folder" in script_name:
            self.setup_png_folder_params()
        elif "view_labels" in script_name:
            self.setup_analyze_labels_params()
        elif "view_mask" in script_name:
            self.setup_visualize_mask_params()
        elif "nde_viewer" in script_name:
            self.setup_view_nde_params()
        elif "copy_random_images" in script_name:
            self.setup_copie_aleatoire_params()
        elif "copy_matching_files" in script_name:
            self.setup_copie_correspondants_params()
        elif "copy_by_resolution" in script_name:
            self.setup_copie_resolution_params()
        elif "util_directory_tree" in script_name:
            self.setup_arborescence_params()
        elif "overlay" in script_name:
            self.setup_overlay_params()
        elif "H5py_NdeToImages" in script_name:
            self.setup_h5py_nde_to_images_params()
        elif "H5py_compareFile" in script_name:
            self.setup_h5py_compare_file_params()
        elif "H5py_enlever_dossier_inutile" in script_name:
            self.setup_h5py_enlever_dossier_params()
        elif "H5py_inversion" in script_name:
            self.setup_h5py_inversion_params()
        elif "H5py_openFile" in script_name:
            self.setup_h5py_open_file_params()
        elif "H5py_process_nde" in script_name:
            self.setup_h5py_process_nde_params()

    def create_path_selector(self, parent, row, label_text, var, is_file=True, file_types=None, path_key=None):
        """Crée un sélecteur de chemin (fichier ou dossier)"""
        ttk.Label(parent, text=label_text).grid(row=row, column=0, sticky=tk.W, pady=2)

        entry = ttk.Entry(parent, textvariable=var, width=60)
        entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))

        if is_file:
            btn_text = "📁 Fichier"
            command = lambda: self.select_file(var, file_types, path_key)
        else:
            btn_text = "📂 Dossier"
            command = lambda: self.select_directory(var, path_key)

        btn = ttk.Button(parent, text=btn_text, command=command)
        btn.grid(row=row, column=2, pady=2)

    def create_output_path_selector(self, parent, row, label_text, var, path_key=None):
        """Crée un sélecteur de chemin pour fichier de sortie (sauvegarde)"""
        ttk.Label(parent, text=label_text).grid(row=row, column=0, sticky=tk.W, pady=2)

        entry = ttk.Entry(parent, textvariable=var, width=60)
        entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))

        btn = ttk.Button(parent, text="💾 Sauvegarder", command=lambda: self.select_output_file(var, path_key))
        btn.grid(row=row, column=2, pady=2)

    def select_output_file(self, var, path_key=None):
        """Ouvre un dialogue de sauvegarde de fichier"""
        # Utiliser le chemin mémorisé comme répertoire initial
        initial_dir = ""
        if path_key:
            script_name = self.script_var.get()
            remembered_path = self.get_remembered_path(script_name, path_key)
            if remembered_path and os.path.exists(os.path.dirname(remembered_path)):
                initial_dir = os.path.dirname(remembered_path)

        filename = filedialog.asksaveasfilename(
            filetypes=[("Tous les fichiers", "*.*")],
            initialdir=initial_dir,
            title="Sélectionner le fichier de sortie"
        )
        if filename:
            var.set(filename)
            # Mémoriser le chemin
            if path_key:
                script_name = self.script_var.get()
                self.remember_path(script_name, path_key, filename)

    def select_file(self, var, file_types=None, path_key=None):
        """Ouvre un dialogue de sélection de fichier"""
        if file_types is None:
            file_types = [("Tous les fichiers", "*.*")]

        # Utiliser le chemin mémorisé comme répertoire initial
        initial_dir = ""
        if path_key:
            script_name = self.script_var.get()
            remembered_path = self.get_remembered_path(script_name, path_key)
            if remembered_path and os.path.exists(os.path.dirname(remembered_path)):
                initial_dir = os.path.dirname(remembered_path)

        filename = filedialog.askopenfilename(filetypes=file_types, initialdir=initial_dir)
        if filename:
            var.set(filename)
            # Mémoriser le chemin
            if path_key:
                script_name = self.script_var.get()
                self.remember_path(script_name, path_key, filename)

    def select_directory(self, var, path_key=None):
        """Ouvre un dialogue de sélection de dossier"""
        # Utiliser le chemin mémorisé comme répertoire initial
        initial_dir = ""
        if path_key:
            script_name = self.script_var.get()
            remembered_path = self.get_remembered_path(script_name, path_key)
            if remembered_path and os.path.exists(remembered_path):
                initial_dir = remembered_path

        dirname = filedialog.askdirectory(initialdir=initial_dir)
        if dirname:
            var.set(dirname)
            # Mémoriser le chemin
            if path_key:
                script_name = self.script_var.get()
                self.remember_path(script_name, path_key, dirname)



    def setup_check_shape_params(self):
        """Configuration pour nifti_check_shape.py"""
        script_name = "nifti_check_shape"

        # Charger le chemin mémorisé
        remembered_path = self.get_remembered_path(script_name, "file_path")
        if remembered_path:
            self.file_path.set(remembered_path)

        self.create_path_selector(self.params_frame, 0, "Fichier NIfTI:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")],
                                 path_key="file_path")

    def setup_npz_check_shape_params(self):
        """Configuration pour npz_check_shape.py"""
        script_name = "npz_check_shape"

        # Charger le chemin mémorisé
        remembered_path = self.get_remembered_path(script_name, "file_path")
        if remembered_path:
            self.file_path.set(remembered_path)

        self.create_path_selector(self.params_frame, 0, "Fichier NPZ:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NPZ", "*.npz"), ("Tous", "*.*")],
                                 path_key="file_path")

        # Description
        ttk.Label(self.params_frame, text="Analyse les dimensions et propriétés d'un fichier NPZ",
                 font=('Arial', 10)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="• Sélection automatique de la meilleure clé (3D uint8)",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• Analyse des valeurs, dimensions et compatibilité",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_h5_check_shape_params(self):
        """Configuration pour h5_check_shape.py"""
        script_name = "h5_check_shape"

        # Charger le chemin mémorisé
        remembered_path = self.get_remembered_path(script_name, "file_path")
        if remembered_path:
            self.file_path.set(remembered_path)

        self.create_path_selector(self.params_frame, 0, "Fichier H5:", self.file_path,
                                 is_file=True, file_types=[("Fichiers H5", "*.h5 *.hdf5"), ("Tous", "*.*")],
                                 path_key="file_path")

        # Description
        ttk.Label(self.params_frame, text="Analyse les dimensions et propriétés d'un fichier H5",
                 font=('Arial', 10)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="• Sélection automatique du meilleur dataset (3D uint8)",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• Analyse des valeurs, métadonnées H5 et compatibilité",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_dataset_2d_to_3d_params(self):
        """Configuration pour conversion_dataset_2d_to_3d.py"""
        script_name = "conversion_dataset_2d_to_3d"

        # Charger les chemins mémorisés
        remembered_input = self.get_remembered_path(script_name, "input_path")
        remembered_labels = self.get_remembered_path(script_name, "labels_path")
        remembered_output = self.get_remembered_path(script_name, "output_path")
        remembered_dataset = self.get_remembered_path(script_name, "dataset_name")

        if remembered_input:
            self.input_path.set(remembered_input)
        if remembered_labels:
            self.labels_path.set(remembered_labels)
        if remembered_output:
            self.output_path.set(remembered_output)
        if remembered_dataset:
            self.dataset_name.set(remembered_dataset)
        else:
            self.dataset_name.set("Dataset_3d")

        self.create_path_selector(self.params_frame, 0, "Dossier images:", self.input_path,
                                 is_file=False, path_key="input_path")
        self.create_path_selector(self.params_frame, 1, "Dossier labels:", self.labels_path,
                                 is_file=False, path_key="labels_path")
        self.create_path_selector(self.params_frame, 2, "Dossier sortie:", self.output_path,
                                 is_file=False, path_key="output_path")

        # Paramètres additionnels
        ttk.Label(self.params_frame, text="Nom du dataset:").grid(row=3, column=0, sticky=tk.W, pady=2)
        dataset_entry = ttk.Entry(self.params_frame, textvariable=self.dataset_name, width=30)
        dataset_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        dataset_entry.bind('<FocusOut>', lambda e: self.remember_path(script_name, "dataset_name", self.dataset_name.get()))

        # Note explicative
        ttk.Label(self.params_frame, text="Mode: Un volume 3D par forme d'image unique",
                 font=('Arial', 8)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Format: 001_0000.nii.gz (images), 001.nii.gz (labels)",
                 font=('Arial', 8)).grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_inference_2d_to_3d_params(self):
        """Configuration pour conversion_inference_2d_to_3d.py"""
        script_name = "conversion_inference_2d_to_3d"

        # Charger les chemins mémorisés
        remembered_input = self.get_remembered_path(script_name, "input_path")
        remembered_output = self.get_remembered_path(script_name, "output_path")

        if remembered_input:
            self.input_path.set(remembered_input)
        if remembered_output:
            self.output_path.set(remembered_output)

        self.create_path_selector(self.params_frame, 0, "Dossier images:", self.input_path,
                                 is_file=False, path_key="input_path")
        self.create_path_selector(self.params_frame, 1, "Dossier sortie:", self.output_path,
                                 is_file=False, path_key="output_path")

        # Note explicative
        ttk.Label(self.params_frame, text="Mode: Un volume 3D par forme d'image unique",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Format: 001_0000.nii.gz, 002_0000.nii.gz, ...",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_transpose_params(self):
        """Configuration pour nifti_transpose.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier d'entrée:", self.input_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")])
        self.create_path_selector(self.params_frame, 1, "Fichier de sortie:", self.output_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")])

    def setup_view_volume_params(self):
        """Configuration pour nifti_view_volume.py"""
        script_name = "nifti_view_volume"

        # Charger le chemin mémorisé
        remembered_path = self.get_remembered_path(script_name, "file_path")
        if remembered_path:
            self.file_path.set(remembered_path)

        self.create_path_selector(self.params_frame, 0, "Fichier NIfTI:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")],
                                 path_key="file_path")

    def setup_npz_view_volume_params(self):
        """Configuration pour npz_view_volume.py"""
        script_name = "npz_view_volume"

        # Charger le chemin mémorisé
        remembered_path = self.get_remembered_path(script_name, "file_path")
        if remembered_path:
            self.file_path.set(remembered_path)

        self.create_path_selector(self.params_frame, 0, "Fichier NPZ:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NPZ", "*.npz"), ("Tous", "*.*")],
                                 path_key="file_path")

    def setup_npz_view_label_params(self):
        """Configuration pour npz_view_label.py"""
        script_name = "npz_view_label"

        # Charger le chemin mémorisé
        remembered_path = self.get_remembered_path(script_name, "file_path")
        if remembered_path:
            self.file_path.set(remembered_path)

        self.create_path_selector(self.params_frame, 0, "Fichier NPZ:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NPZ", "*.npz"), ("Tous", "*.*")],
                                 path_key="file_path")

        # Description
        ttk.Label(self.params_frame, text="Analyse les labels (valeurs uniques) présents dans le fichier NPZ",
                 font=('Arial', 10)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="• Sélection automatique de la meilleure clé (3D uint8)",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• Affiche les statistiques détaillées par label",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• Analyse par slice pour les volumes 3D",
                 font=('Arial', 8)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_nifti_export_png_params(self):
        """Configuration pour nifti_export_png.py"""
        script_name = "nifti_export_png"

        # Charger les chemins mémorisés
        remembered_input = self.get_remembered_path(script_name, "input_path")
        remembered_output = self.get_remembered_path(script_name, "output_path")

        if remembered_input:
            self.file_path.set(remembered_input)
        if remembered_output:
            self.output_path.set(remembered_output)

        # Valeurs par défaut
        self.export_prefix.set("slice")
        self.export_dpi.set("")  # Vide = résolution automatique
        self.export_image_type.set("auto")

        self.create_path_selector(self.params_frame, 0, "Fichier NIfTI:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")],
                                 path_key="input_path")

        self.create_path_selector(self.params_frame, 1, "Dossier de sortie:", self.output_path,
                                 is_file=False, path_key="output_path")

        # Paramètres d'export
        ttk.Label(self.params_frame, text="Préfixe des fichiers:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_prefix, width=20).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Résolution (DPI):").grid(row=3, column=0, sticky=tk.W, pady=2)
        dpi_entry = ttk.Entry(self.params_frame, textvariable=self.export_dpi, width=10)
        dpi_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="(vide = auto)", font=('Arial', 8)).grid(row=3, column=2, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Type d'images:").grid(row=4, column=0, sticky=tk.W, pady=2)
        image_type_combo = ttk.Combobox(self.params_frame, textvariable=self.export_image_type, width=15, state="readonly")
        image_type_combo['values'] = ["auto", "imagesTr", "labelsTr"]
        image_type_combo.grid(row=4, column=1, sticky=tk.W, pady=2)

        # Export partiel (optionnel)
        ttk.Label(self.params_frame, text="Slice début (optionnel):").grid(row=5, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_start_slice, width=10).grid(row=5, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Slice fin (optionnel):").grid(row=6, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_end_slice, width=10).grid(row=6, column=1, sticky=tk.W, pady=2)

        # Option format de couleur
        ttk.Checkbutton(self.params_frame, text="Export en uint8 (sinon RGB/couleurs distinctes)",
                       variable=self.export_uint8_mode).grid(row=7, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Description
        ttk.Label(self.params_frame, text="Exporte toutes les slices d'un volume NIfTI en images PNG",
                 font=('Arial', 10)).grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="• imagesTr : Images normales (uint8=grayscale, RGB=colorisé)",
                 font=('Arial', 8)).grid(row=9, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• labelsTr : Masques (uint8=classes directes, RGB=couleurs distinctes)",
                 font=('Arial', 8)).grid(row=10, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• Résolution auto : adaptée aux dimensions du volume",
                 font=('Arial', 8)).grid(row=11, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_nifti_export_dossier_png_params(self):
        """Configuration pour nifti_export_dossier_png.py"""
        # Valeurs par défaut
        self.export_prefix.set("slice")
        self.export_dpi.set("")  # Vide = résolution automatique
        self.export_image_type.set("auto")

        self.create_path_selector(self.params_frame, 0, "Dossier NIfTI:", self.file_path,
                                 is_file=False, path_key="input_folder")

        self.create_path_selector(self.params_frame, 1, "Dossier de sortie:", self.output_path,
                                 is_file=False, path_key="output_folder")

        # Paramètres d'export
        ttk.Label(self.params_frame, text="Préfixe des fichiers:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_prefix, width=20).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Résolution (DPI):").grid(row=3, column=0, sticky=tk.W, pady=2)
        dpi_entry = ttk.Entry(self.params_frame, textvariable=self.export_dpi, width=10)
        dpi_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="(vide = auto)", font=('Arial', 8)).grid(row=3, column=2, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Type d'images:").grid(row=4, column=0, sticky=tk.W, pady=2)
        image_type_combo = ttk.Combobox(self.params_frame, textvariable=self.export_image_type, width=15, state="readonly")
        image_type_combo['values'] = ["auto", "imagesTr", "labelsTr"]
        image_type_combo.grid(row=4, column=1, sticky=tk.W, pady=2)

        # Option format de couleur
        ttk.Checkbutton(self.params_frame, text="Export en uint8 (sinon RGB/couleurs distinctes)",
                       variable=self.export_uint8_mode).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Description
        ttk.Label(self.params_frame, text="Exporte tous les fichiers NIfTI d'un dossier en images PNG",
                 font=('Arial', 10)).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Crée un sous-dossier pour chaque fichier traité",
                 font=('Arial', 8)).grid(row=7, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_npz_export_png_params(self):
        """Configuration pour npz_export_png.py"""
        script_name = "npz_export_png"

        # Charger les chemins mémorisés
        remembered_input = self.get_remembered_path(script_name, "input_path")
        remembered_output = self.get_remembered_path(script_name, "output_path")

        if remembered_input:
            self.file_path.set(remembered_input)
        if remembered_output:
            self.output_path.set(remembered_output)

        # Valeurs par défaut
        self.export_prefix.set("slice")
        self.export_dpi.set("")  # Vide = résolution automatique
        self.export_image_type.set("auto")

        self.create_path_selector(self.params_frame, 0, "Fichier NPZ:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NPZ", "*.npz"), ("Tous", "*.*")],
                                 path_key="input_path")

        self.create_path_selector(self.params_frame, 1, "Dossier de sortie:", self.output_path,
                                 is_file=False, path_key="output_path")

        # Paramètres d'export
        ttk.Label(self.params_frame, text="Préfixe des fichiers:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_prefix, width=20).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Résolution (DPI):").grid(row=3, column=0, sticky=tk.W, pady=2)
        dpi_entry = ttk.Entry(self.params_frame, textvariable=self.export_dpi, width=10)
        dpi_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="(vide = auto)", font=('Arial', 8)).grid(row=3, column=2, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Type d'images:").grid(row=4, column=0, sticky=tk.W, pady=2)
        image_type_combo = ttk.Combobox(self.params_frame, textvariable=self.export_image_type, width=15, state="readonly")
        image_type_combo['values'] = ["auto", "imagesTr", "labelsTr"]
        image_type_combo.grid(row=4, column=1, sticky=tk.W, pady=2)

        # Export partiel (optionnel)
        ttk.Label(self.params_frame, text="Slice début (optionnel):").grid(row=5, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_start_slice, width=10).grid(row=5, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Slice fin (optionnel):").grid(row=6, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_end_slice, width=10).grid(row=6, column=1, sticky=tk.W, pady=2)

        # Option format de couleur
        ttk.Checkbutton(self.params_frame, text="Export en uint8 (sinon RGB/couleurs distinctes)",
                       variable=self.export_uint8_mode).grid(row=7, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Description
        ttk.Label(self.params_frame, text="Exporte toutes les slices d'un volume NPZ en images PNG",
                 font=('Arial', 10)).grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Gère automatiquement la sélection de clé pour les fichiers NPZ multi-arrays",
                 font=('Arial', 8)).grid(row=9, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• imagesTr : Images normales (uint8=grayscale, RGB=colorisé)",
                 font=('Arial', 8)).grid(row=10, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• labelsTr : Masques (uint8=classes directes, RGB=couleurs distinctes)",
                 font=('Arial', 8)).grid(row=11, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_h5_view_volume_params(self):
        """Configuration pour h5_view_volume.py"""
        script_name = "h5_view_volume"

        # Charger le chemin mémorisé
        remembered_path = self.get_remembered_path(script_name, "file_path")
        if remembered_path:
            self.file_path.set(remembered_path)

        # Valeurs par défaut
        self.h5_colorize_mode.set("auto")

        self.create_path_selector(self.params_frame, 0, "Fichier H5:", self.file_path,
                                 is_file=True, file_types=[("Fichiers H5", "*.h5"), ("Fichiers HDF5", "*.hdf5"), ("Tous", "*.*")],
                                 path_key="file_path")

        # Mode de colorisation
        ttk.Label(self.params_frame, text="Mode colorisation:").grid(row=1, column=0, sticky=tk.W, pady=2)
        colorize_combo = ttk.Combobox(self.params_frame, textvariable=self.h5_colorize_mode, width=15, state="readonly")
        colorize_combo['values'] = ["auto", "grayscale", "omniscan"]
        colorize_combo.grid(row=1, column=1, sticky=tk.W, pady=2)

        # Description
        ttk.Label(self.params_frame, text="Visualise les volumes 3D contenus dans les fichiers H5/HDF5",
                 font=('Arial', 10)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="• auto : Détection automatique (OmniScan pour NDE, grayscale pour autres)",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• grayscale : Toujours en niveaux de gris",
                 font=('Arial', 8)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• omniscan : Toujours avec les couleurs OmniScan (si disponible)",
                 font=('Arial', 8)).grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_npz_export_dossier_png_params(self):
        """Configuration pour npz_export_dossier_png.py"""
        # Valeurs par défaut
        self.export_prefix.set("slice")
        self.export_dpi.set("")  # Vide = résolution automatique
        self.export_image_type.set("auto")

        self.create_path_selector(self.params_frame, 0, "Dossier NPZ:", self.file_path,
                                 is_file=False, path_key="input_folder")

        self.create_path_selector(self.params_frame, 1, "Dossier de sortie:", self.output_path,
                                 is_file=False, path_key="output_folder")

        # Paramètres d'export
        ttk.Label(self.params_frame, text="Préfixe des fichiers:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_prefix, width=20).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Résolution (DPI):").grid(row=3, column=0, sticky=tk.W, pady=2)
        dpi_entry = ttk.Entry(self.params_frame, textvariable=self.export_dpi, width=10)
        dpi_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="(vide = auto)", font=('Arial', 8)).grid(row=3, column=2, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Type d'images:").grid(row=4, column=0, sticky=tk.W, pady=2)
        image_type_combo = ttk.Combobox(self.params_frame, textvariable=self.export_image_type, width=15, state="readonly")
        image_type_combo['values'] = ["auto", "imagesTr", "labelsTr"]
        image_type_combo.grid(row=4, column=1, sticky=tk.W, pady=2)

        # Option format de couleur
        ttk.Checkbutton(self.params_frame, text="Export en uint8 (sinon RGB/couleurs distinctes)",
                       variable=self.export_uint8_mode).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Description
        ttk.Label(self.params_frame, text="Exporte tous les fichiers NPZ d'un dossier en images PNG",
                 font=('Arial', 10)).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Crée un sous-dossier pour chaque fichier traité",
                 font=('Arial', 8)).grid(row=7, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_h5_export_png_params(self):
        """Configuration pour h5_export_png.py"""
        script_name = "h5_export_png"

        # Charger les chemins mémorisés
        remembered_input = self.get_remembered_path(script_name, "input_path")
        remembered_output = self.get_remembered_path(script_name, "output_path")

        if remembered_input:
            self.file_path.set(remembered_input)
        if remembered_output:
            self.output_path.set(remembered_output)

        # Valeurs par défaut
        self.export_prefix.set("slice")
        self.export_dpi.set("")  # Vide = résolution automatique
        self.export_image_type.set("auto")

        self.create_path_selector(self.params_frame, 0, "Fichier H5:", self.file_path,
                                 is_file=True, file_types=[("Fichiers H5", "*.h5"), ("Fichiers HDF5", "*.hdf5"), ("Tous", "*.*")],
                                 path_key="input_path")

        self.create_path_selector(self.params_frame, 1, "Dossier de sortie:", self.output_path,
                                 is_file=False, path_key="output_path")

        # Paramètres d'export
        ttk.Label(self.params_frame, text="Préfixe des fichiers:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_prefix, width=20).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Résolution (DPI):").grid(row=3, column=0, sticky=tk.W, pady=2)
        dpi_entry = ttk.Entry(self.params_frame, textvariable=self.export_dpi, width=10)
        dpi_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="(vide = auto)", font=('Arial', 8)).grid(row=3, column=2, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Type d'images:").grid(row=4, column=0, sticky=tk.W, pady=2)
        image_type_combo = ttk.Combobox(self.params_frame, textvariable=self.export_image_type, width=15, state="readonly")
        image_type_combo['values'] = ["auto", "imagesTr", "labelsTr"]
        image_type_combo.grid(row=4, column=1, sticky=tk.W, pady=2)

        # Export partiel (optionnel)
        ttk.Label(self.params_frame, text="Slice début (optionnel):").grid(row=5, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_start_slice, width=10).grid(row=5, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Slice fin (optionnel):").grid(row=6, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_end_slice, width=10).grid(row=6, column=1, sticky=tk.W, pady=2)

        # Option format de couleur
        ttk.Checkbutton(self.params_frame, text="Export en uint8 (sinon RGB/couleurs OmniScan)",
                       variable=self.export_uint8_mode).grid(row=7, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Description
        ttk.Label(self.params_frame, text="Exporte toutes les slices d'un volume H5 en images PNG",
                 font=('Arial', 10)).grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Gère automatiquement la sélection de dataset pour les fichiers H5 multi-datasets",
                 font=('Arial', 8)).grid(row=9, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• Données NDE : uint8=grayscale, RGB=colormap OmniScan",
                 font=('Arial', 8)).grid(row=10, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• imagesTr/labelsTr : même logique que NIfTI/NPZ",
                 font=('Arial', 8)).grid(row=11, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_h5_export_dossier_png_params(self):
        """Configuration pour h5_export_dossier_png.py"""
        # Valeurs par défaut
        self.export_prefix.set("slice")
        self.export_dpi.set("")  # Vide = résolution automatique
        self.export_image_type.set("auto")

        self.create_path_selector(self.params_frame, 0, "Dossier H5:", self.file_path,
                                 is_file=False, path_key="input_folder")

        self.create_path_selector(self.params_frame, 1, "Dossier de sortie:", self.output_path,
                                 is_file=False, path_key="output_folder")

        # Paramètres d'export
        ttk.Label(self.params_frame, text="Préfixe des fichiers:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.export_prefix, width=20).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Résolution (DPI):").grid(row=3, column=0, sticky=tk.W, pady=2)
        dpi_entry = ttk.Entry(self.params_frame, textvariable=self.export_dpi, width=10)
        dpi_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="(vide = auto)", font=('Arial', 8)).grid(row=3, column=2, sticky=tk.W, pady=2)

        ttk.Label(self.params_frame, text="Type d'images:").grid(row=4, column=0, sticky=tk.W, pady=2)
        image_type_combo = ttk.Combobox(self.params_frame, textvariable=self.export_image_type, width=15, state="readonly")
        image_type_combo['values'] = ["auto", "imagesTr", "labelsTr"]
        image_type_combo.grid(row=4, column=1, sticky=tk.W, pady=2)

        # Option format de couleur
        ttk.Checkbutton(self.params_frame, text="Export en uint8 (sinon RGB/couleurs OmniScan)",
                       variable=self.export_uint8_mode).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Description
        ttk.Label(self.params_frame, text="Exporte tous les fichiers H5 d'un dossier en images PNG",
                 font=('Arial', 10)).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Crée un sous-dossier pour chaque fichier traité",
                 font=('Arial', 8)).grid(row=7, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="Gère automatiquement les datasets et colormaps OmniScan",
                 font=('Arial', 8)).grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_pngs_to_volume_params(self):
        """Configuration pour pngs_to_volume.py"""
        script_name = "pngs_to_volume"

        # Valeurs par défaut
        if not hasattr(self, 'png_folder_path'):
            self.png_folder_path = tk.StringVar()
        if not hasattr(self, 'volume_output_path'):
            self.volume_output_path = tk.StringVar()
        if not hasattr(self, 'volume_format'):
            self.volume_format = tk.StringVar(value="h5")
        if not hasattr(self, 'volume_color_mode'):
            self.volume_color_mode = tk.StringVar(value="L")
        if not hasattr(self, 'volume_axis_order'):
            self.volume_axis_order = tk.StringVar(value="ZYX")

        # Charger les chemins mémorisés
        remembered_input = self.get_remembered_path(script_name, "png_folder_path")
        if remembered_input:
            self.png_folder_path.set(remembered_input)

        remembered_output = self.get_remembered_path(script_name, "volume_output_path")
        if remembered_output:
            self.volume_output_path.set(remembered_output)

        # Dossier d'entrée (PNGs)
        self.create_path_selector(self.params_frame, 0, "Dossier PNG:", self.png_folder_path,
                                 is_file=False, path_key="png_folder_path")

        # Fichier de sortie
        self.create_output_path_selector(self.params_frame, 1, "Fichier sortie:", self.volume_output_path, path_key="volume_output_path")

        # Format de sortie
        ttk.Label(self.params_frame, text="Format de sortie:").grid(row=2, column=0, sticky=tk.W, pady=2)
        format_combo = ttk.Combobox(self.params_frame, textvariable=self.volume_format,
                                   values=["h5", "npz", "nifti"], state="readonly", width=15)
        format_combo.grid(row=2, column=1, sticky=tk.W, pady=2)

        # Mode couleur
        ttk.Label(self.params_frame, text="Mode couleur:").grid(row=3, column=0, sticky=tk.W, pady=2)
        color_combo = ttk.Combobox(self.params_frame, textvariable=self.volume_color_mode,
                                  values=["L", "RGB"], state="readonly", width=15)
        color_combo.grid(row=3, column=1, sticky=tk.W, pady=2)

        # Ordre des axes
        ttk.Label(self.params_frame, text="Ordre des axes:").grid(row=4, column=0, sticky=tk.W, pady=2)
        axis_combo = ttk.Combobox(self.params_frame, textvariable=self.volume_axis_order,
                                 values=["ZYX", "XYZ", "YXZ"], state="readonly", width=15)
        axis_combo.grid(row=4, column=1, sticky=tk.W, pady=2)

        # Descriptions
        ttk.Label(self.params_frame, text="Convertit un dossier de PNGs en volume 3D",
                 font=('Arial', 10)).grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="• L = Grayscale (1 canal), RGB = Couleur (3 canaux)",
                 font=('Arial', 8)).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• ZYX = Ordre standard (Z=profondeur, Y=hauteur, X=largeur)",
                 font=('Arial', 8)).grid(row=7, column=0, columnspan=3, sticky=tk.W, pady=1)
        ttk.Label(self.params_frame, text="• Fichier sortie : chemin sans extension (sera ajoutée automatiquement)",
                 font=('Arial', 8)).grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=1)

    def setup_png_folder_params(self):
        """Configuration pour view_png_folder.py"""
        # Initialiser les valeurs par défaut
        self.max_images.set("")

        self.create_path_selector(self.params_frame, 0, "Dossier PNG:", self.input_path, is_file=False)

        ttk.Label(self.params_frame, text="Nombre max d'images (optionnel):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.max_images, width=10).grid(row=1, column=1, sticky=tk.W, pady=2)

        # Note explicative
        ttk.Label(self.params_frame, text="(Laissez vide pour toutes les images)", font=('Arial', 8)).grid(row=2, column=1, sticky=tk.W, pady=2)

    def setup_analyze_labels_params(self):
        """Configuration pour view_labels.py"""
        self.create_path_selector(self.params_frame, 0, "Dossier labels:", self.input_path, is_file=False)

    def setup_visualize_mask_params(self):
        """Configuration pour view_mask.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier image:", self.file_path,
                                 is_file=True, file_types=[("Images PNG", "*.png"), ("Tous", "*.*")])

    def setup_view_nde_params(self):
        """Configuration pour nde_viewer.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier NDE:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NDE", "*.nde"), ("Fichiers HDF5", "*.h5"), ("Tous", "*.*")])

    def setup_copie_aleatoire_params(self):
        """Configuration pour copy_random_images.py"""
        ttk.Label(self.params_frame, text="Ce script copie aléatoirement des images d'un dossier vers un autre.",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Les paramètres sont configurés dans le script (nombre d'images, préfixe/suffixe, etc.).",
                 font=('Arial', 8)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Le script ouvrira des dialogues pour sélectionner les dossiers source et destination.",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_copie_correspondants_params(self):
        """Configuration pour copy_matching_files.py"""
        ttk.Label(self.params_frame, text="Ce script copie les fichiers correspondants entre deux dossiers.",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Il copie les fichiers du dossier A vers C et les fichiers correspondants du dossier B vers D.",
                 font=('Arial', 8)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Le script ouvrira des dialogues pour sélectionner les 4 dossiers nécessaires.",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_copie_resolution_params(self):
        """Configuration pour copy_by_resolution.py"""
        ttk.Label(self.params_frame, text="Ce script copie des fichiers en les groupant par résolution d'image.",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Il sélectionne jusqu'à 3 fichiers aléatoirement pour chaque résolution unique.",
                 font=('Arial', 8)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Le script ouvrira des dialogues pour sélectionner les 4 dossiers nécessaires.",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Nécessite le module Pillow (PIL) pour analyser les résolutions d'images.",
                 font=('Arial', 8), foreground='blue').grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_arborescence_params(self):
        """Configuration pour util_directory_tree.py"""
        script_name = self.script_var.get()

        # Dossier à analyser
        ttk.Label(self.params_frame, text="Dossier à analyser:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.input_path, width=50).grid(row=0, column=1, sticky=tk.W, pady=2)
        ttk.Button(self.params_frame, text="Parcourir",
                  command=lambda: self.select_directory(self.input_path, "input_path")).grid(row=0, column=2, padx=5, pady=2)

        # Options
        ttk.Label(self.params_frame, text="Options:").grid(row=1, column=0, sticky=tk.W, pady=5)

        # Checkbox pour afficher les fichiers
        self.show_files_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(self.params_frame, text="Afficher les fichiers (pas seulement les dossiers)",
                       variable=self.show_files_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)

        # Niveau maximum
        ttk.Label(self.params_frame, text="Niveau maximum de profondeur:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.max_depth_var = tk.StringVar(value="5")
        depth_entry = ttk.Entry(self.params_frame, textvariable=self.max_depth_var, width=10)
        depth_entry.grid(row=3, column=1, sticky=tk.W, pady=2)

        # Description
        ttk.Label(self.params_frame, text="Ce script affiche l'arborescence d'un dossier et sauvegarde le résultat dans 'structure.txt'.",
                 font=('Arial', 10)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=10)

    def setup_overlay_params(self):
        """Configuration pour overlay.py"""
        script_name = "overlay"

        # Variables pour les paramètres
        self.overlay_image_dir_var = tk.StringVar()
        self.overlay_mask_dir_var = tk.StringVar()
        self.overlay_output_dir_var = tk.StringVar()

        # Charger les chemins mémorisés
        remembered_image_dir = self.get_remembered_path(script_name, "image_dir")
        remembered_mask_dir = self.get_remembered_path(script_name, "mask_dir")
        remembered_output_dir = self.get_remembered_path(script_name, "output_dir")

        if remembered_image_dir:
            self.overlay_image_dir_var.set(remembered_image_dir)
        if remembered_mask_dir:
            self.overlay_mask_dir_var.set(remembered_mask_dir)
        if remembered_output_dir:
            self.overlay_output_dir_var.set(remembered_output_dir)

        # Interface
        ttk.Label(self.params_frame, text="Crée des overlays avec les couleurs exactes des masques originaux",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)

        self.create_path_selector(self.params_frame, 1, "Dossier des images:", self.overlay_image_dir_var,
                                 is_file=False, path_key="image_dir")

        self.create_path_selector(self.params_frame, 2, "Dossier des masques:", self.overlay_mask_dir_var,
                                 is_file=False, path_key="mask_dir")

        self.create_path_selector(self.params_frame, 3, "Dossier de sortie:", self.overlay_output_dir_var,
                                 is_file=False, path_key="output_dir")

        # Description
        ttk.Label(self.params_frame, text="• Utilise les couleurs exactes du masque original (sans cycle de couleurs)",
                 font=('Arial', 8)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• Supprime automatiquement le fond noir des masques",
                 font=('Arial', 8)).grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• Applique une transparence de 60% pour voir l'image sous-jacente",
                 font=('Arial', 8)).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_h5py_nde_to_images_params(self):
        """Configuration pour H5py_NdeToImages.py"""
        script_name = "H5py_NdeToImages"

        # Variables pour les paramètres
        self.h5py_files_var = tk.StringVar()
        self.h5py_output_var = tk.StringVar()

        # Charger les chemins mémorisés
        remembered_files = self.get_remembered_path(script_name, "files_path")
        remembered_output = self.get_remembered_path(script_name, "output_path")

        if remembered_files:
            self.h5py_files_var.set(remembered_files)
        if remembered_output:
            self.h5py_output_var.set(remembered_output)

        # Interface
        ttk.Label(self.params_frame, text="Fichiers NDE (séparés par des virgules):").grid(row=0, column=0, sticky=tk.W, pady=2)
        files_entry = ttk.Entry(self.params_frame, textvariable=self.h5py_files_var, width=60)
        files_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(self.params_frame, text="📁 Fichiers",
                  command=lambda: self.select_multiple_files(self.h5py_files_var,
                                                           [("Fichiers NDE", "*.nde"), ("Tous", "*.*")],
                                                           "files_path")).grid(row=0, column=2, pady=2)

        self.create_path_selector(self.params_frame, 1, "Dossier de sortie:", self.h5py_output_var,
                                 is_file=False, path_key="output_path")

        # Description
        ttk.Label(self.params_frame, text="Convertit les fichiers NDE en images PNG (grayscale et colorisées)",
                 font=('Arial', 10)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(self.params_frame, text="Supporte les structures Domain et Public des fichiers NDE",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_h5py_compare_file_params(self):
        """Configuration pour H5py_compareFile.py"""
        script_name = "H5py_compareFile"

        # Variables pour les paramètres
        self.h5py_file1_var = tk.StringVar()
        self.h5py_file2_var = tk.StringVar()
        self.h5py_output_dir_var = tk.StringVar()

        # Charger les chemins mémorisés
        remembered_file1 = self.get_remembered_path(script_name, "file1_path")
        remembered_file2 = self.get_remembered_path(script_name, "file2_path")
        remembered_output = self.get_remembered_path(script_name, "output_dir")

        if remembered_file1:
            self.h5py_file1_var.set(remembered_file1)
        if remembered_file2:
            self.h5py_file2_var.set(remembered_file2)
        if remembered_output:
            self.h5py_output_dir_var.set(remembered_output)
        else:
            self.h5py_output_dir_var.set("./")

        # Interface
        ttk.Label(self.params_frame, text="Compare deux fichiers NDE et génère un rapport de différences",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)

        self.create_path_selector(self.params_frame, 1, "Fichier NDE 1:", self.h5py_file1_var,
                                 is_file=True, file_types=[("Fichiers NDE", "*.nde"), ("Tous", "*.*")],
                                 path_key="file1_path")

        self.create_path_selector(self.params_frame, 2, "Fichier NDE 2:", self.h5py_file2_var,
                                 is_file=True, file_types=[("Fichiers NDE", "*.nde"), ("Tous", "*.*")],
                                 path_key="file2_path")

        self.create_path_selector(self.params_frame, 3, "Dossier de sortie:", self.h5py_output_dir_var,
                                 is_file=False, path_key="output_dir")

        # Description
        ttk.Label(self.params_frame, text="Génère: differences.txt et structure_*.json",
                 font=('Arial', 8)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=2)



    def setup_h5py_enlever_dossier_params(self):
        """Configuration pour H5py_enlever_dossier_inutile.py"""
        script_name = "H5py_enlever_dossier_inutile"

        # Variables pour les paramètres
        self.h5py_root_dir_var = tk.StringVar()

        # Charger les chemins mémorisés
        remembered_root = self.get_remembered_path(script_name, "root_dir")

        if remembered_root:
            self.h5py_root_dir_var.set(remembered_root)

        # Interface
        ttk.Label(self.params_frame, text="Nettoie la structure des dossiers d'images générées",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)

        self.create_path_selector(self.params_frame, 1, "Dossier racine des images:", self.h5py_root_dir_var,
                                 is_file=False, path_key="root_dir")

        # Description
        ttk.Label(self.params_frame, text="• Déplace le contenu des dossiers 'no_label' vers le parent",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• Supprime les dossiers 'complete' en déplaçant leur contenu",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_h5py_inversion_params(self):
        """Configuration pour H5py_inversion.py"""
        script_name = "H5py_inversion"

        # Variables pour les paramètres
        self.h5py_inversion_dir_var = tk.StringVar()

        # Charger les chemins mémorisés
        remembered_dir = self.get_remembered_path(script_name, "inversion_dir")

        if remembered_dir:
            self.h5py_inversion_dir_var.set(remembered_dir)

        # Interface
        ttk.Label(self.params_frame, text="Inverse les couleurs des images uint8 (255 - valeur_pixel)",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)

        self.create_path_selector(self.params_frame, 1, "Dossier racine des images:", self.h5py_inversion_dir_var,
                                 is_file=False, path_key="inversion_dir")

        # Description
        ttk.Label(self.params_frame, text="• Crée automatiquement un backup avant inversion",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• Traite tous les dossiers 'endviews_uint8' trouvés",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_h5py_open_file_params(self):
        """Configuration pour H5py_openFile.py"""
        script_name = "H5py_openFile"

        # Variables pour les paramètres
        self.h5py_open_file_var = tk.StringVar()

        # Charger les chemins mémorisés
        remembered_file = self.get_remembered_path(script_name, "nde_file")

        if remembered_file:
            self.h5py_open_file_var.set(remembered_file)

        # Interface
        ttk.Label(self.params_frame, text="Ouvre et explore la structure d'un fichier NDE",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)

        self.create_path_selector(self.params_frame, 1, "Fichier NDE à explorer:", self.h5py_open_file_var,
                                 is_file=True, file_types=[("Fichiers NDE", "*.nde"), ("Tous", "*.*")],
                                 path_key="nde_file")

        # Description
        ttk.Label(self.params_frame, text="Affiche les groupes, datasets et métadonnées",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_h5py_process_nde_params(self):
        """Configuration pour H5py_process_nde.py"""
        script_name = "H5py_process_nde"

        # Variables pour les paramètres
        self.h5py_source_dir_var = tk.StringVar()
        self.h5py_dest_dir_var = tk.StringVar()
        self.h5py_preserve_folders_var = tk.BooleanVar()
        self.h5py_confirm_execution_var = tk.BooleanVar()

        # Charger les chemins mémorisés
        remembered_source = self.get_remembered_path(script_name, "source_dir")
        remembered_dest = self.get_remembered_path(script_name, "dest_dir")

        if remembered_source:
            self.h5py_source_dir_var.set(remembered_source)
        if remembered_dest:
            self.h5py_dest_dir_var.set(remembered_dest)

        # Valeurs par défaut pour les nouvelles options
        self.h5py_preserve_folders_var.set(True)  # Préserver par défaut
        self.h5py_confirm_execution_var.set(True)  # Demander confirmation par défaut

        # Interface
        ttk.Label(self.params_frame, text="Traite automatiquement tous les fichiers NDE d'un dossier",
                 font=('Arial', 10)).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)

        self.create_path_selector(self.params_frame, 1, "Dossier source (fichiers NDE):", self.h5py_source_dir_var,
                                 is_file=False, path_key="source_dir")

        self.create_path_selector(self.params_frame, 2, "Dossier destination (images):", self.h5py_dest_dir_var,
                                 is_file=False, path_key="dest_dir")

        # Options
        options_frame = ttk.LabelFrame(self.params_frame, text="Options", padding="5")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        # Option pour préserver les dossiers parents
        preserve_check = ttk.Checkbutton(options_frame, text="Préserver la structure des dossiers parents (ex: 'From ...')",
                                       variable=self.h5py_preserve_folders_var)
        preserve_check.grid(row=0, column=0, sticky=tk.W, pady=2)

        # Option pour demander confirmation
        confirm_check = ttk.Checkbutton(options_frame, text="Demander confirmation avant d'exécuter le script",
                                      variable=self.h5py_confirm_execution_var)
        confirm_check.grid(row=1, column=0, sticky=tk.W, pady=2)

        # Description
        ttk.Label(self.params_frame, text="• Recherche récursivement les fichiers .nde",
                 font=('Arial', 8)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• Évite de retraiter les fichiers déjà traités",
                 font=('Arial', 8)).grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="• Normalise les noms de fichiers pour éviter les doublons",
                 font=('Arial', 8)).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=2)



    def select_multiple_files(self, var, file_types=None, path_key=None):
        """Ouvre un dialogue de sélection de fichiers multiples"""
        if file_types is None:
            file_types = [("Tous les fichiers", "*.*")]

        # Utiliser le chemin mémorisé comme répertoire initial
        initial_dir = ""
        if path_key:
            script_name = self.script_var.get()
            remembered_path = self.get_remembered_path(script_name, path_key)
            if remembered_path and os.path.exists(os.path.dirname(remembered_path.split(',')[0])):
                initial_dir = os.path.dirname(remembered_path.split(',')[0])

        filenames = filedialog.askopenfilenames(filetypes=file_types, initialdir=initial_dir)
        if filenames:
            files_str = ','.join(filenames)
            var.set(files_str)
            # Mémoriser le chemin
            if path_key:
                script_name = self.script_var.get()
                self.remember_path(script_name, path_key, files_str)

    def execute_script(self):
        """Exécute le script sélectionné avec les paramètres fournis"""
        script_name = self.script_var.get()
        if not script_name:
            messagebox.showerror("Erreur", "Veuillez sélectionner un script")
            return

        # Effacer la sortie précédente
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"🚀 Exécution de {script_name}...\n\n")
        self.output_text.update()

        # Désactiver le bouton pendant l'exécution
        self.execute_btn.config(state='disabled')

        # Exécuter dans un thread séparé pour ne pas bloquer l'interface
        thread = threading.Thread(target=self._run_script_thread, args=(script_name,))
        thread.daemon = True
        thread.start()

    def _run_script_thread(self, script_name):
        """Exécute le script dans un thread séparé"""
        try:
            if "nifti_check_shape" in script_name:
                self._run_check_shape()
            elif "npz_check_shape" in script_name:
                self._run_npz_check_shape()
            elif "h5_check_shape" in script_name:
                self._run_h5_check_shape()
            elif "conversion_dataset_2d_to_3d" in script_name:
                self._run_dataset_2d_to_3d()
            elif "conversion_inference_2d_to_3d" in script_name:
                self._run_inference_2d_to_3d()
            elif "nifti_transpose" in script_name:
                self._run_transpose()
            elif "nifti_view_volume" in script_name:
                self._run_view_volume()
            elif "nifti_export_png" in script_name and "dossier" not in script_name:
                self._run_nifti_export_png()
            elif "nifti_export_dossier_png" in script_name:
                self._run_nifti_export_dossier_png()
            elif "npz_view_volume" in script_name:
                self._run_npz_view_volume()
            elif "npz_export_png" in script_name and "dossier" not in script_name:
                self._run_npz_export_png()
            elif "npz_export_dossier_png" in script_name:
                self._run_npz_export_dossier_png()
            elif "h5_view_volume" in script_name:
                self._run_h5_view_volume()
            elif "h5_export_png" in script_name and "dossier" not in script_name:
                self._run_h5_export_png()
            elif "h5_export_dossier_png" in script_name:
                self._run_h5_export_dossier_png()
            elif "pngs_to_volume" in script_name:
                self._run_pngs_to_volume()
            elif "view_png_folder" in script_name:
                self._run_png_folder()
            elif "view_labels" in script_name:
                self._run_analyze_labels()
            elif "view_mask" in script_name:
                self._run_visualize_mask()
            elif "nde_viewer" in script_name:
                self._run_view_nde()
            elif "copy_random_images" in script_name:
                self._run_copie_aleatoire()
            elif "copy_matching_files" in script_name:
                self._run_copie_correspondants()
            elif "copy_by_resolution" in script_name:
                self._run_copie_resolution()
            elif "util_directory_tree" in script_name:
                self._run_arborescence()
            elif "overlay" in script_name:
                self._run_overlay()
            elif "H5py_NdeToImages" in script_name:
                self._run_h5py_nde_to_images()
            elif "H5py_compareFile" in script_name:
                self._run_h5py_compare_file()
            elif "H5py_enlever_dossier_inutile" in script_name:
                self._run_h5py_enlever_dossier()
            elif "H5py_inversion" in script_name:
                self._run_h5py_inversion()
            elif "H5py_openFile" in script_name:
                self._run_h5py_open_file()
            elif "H5py_process_nde" in script_name:
                self._run_h5py_process_nde()
        except Exception as e:
            self.append_output(f"[ERREUR] Erreur: {str(e)}\n")
        finally:
            # Réactiver le bouton de manière thread-safe
            try:
                self.root.after_idle(lambda: self._reactivate_button())
            except Exception:
                pass  # Ignorer les erreurs si la fenêtre est fermée

    def _reactivate_button(self):
        """Réactive le bouton d'exécution"""
        try:
            self.execute_btn.config(state='normal')
        except Exception:
            pass  # Ignorer si le widget n'existe plus

    def append_output(self, text):
        """Ajoute du texte à la zone de sortie de manière thread-safe"""
        try:
            if threading.current_thread() == threading.main_thread():
                # Si on est déjà dans le thread principal, pas besoin d'after()
                self._append_output_main_thread(text)
            else:
                # Sinon, utiliser after() pour passer au thread principal
                self.root.after_idle(lambda: self._append_output_main_thread(text))
        except Exception as e:
            print(f"Erreur dans append_output: {e}")

    def _append_output_main_thread(self, text):
        """Ajoute du texte à la zone de sortie (thread principal)"""
        try:
            self.output_text.insert(tk.END, text)
            self.output_text.see(tk.END)
            self.output_text.update_idletasks()  # Plus sûr que update()
        except Exception as e:
            print(f"Erreur dans _append_output_main_thread: {e}")

    def run_python_script(self, script_path, args=None):
        """Exécute un script Python avec des arguments"""
        cmd = [sys.executable, script_path]
        if args:
            cmd.extend(args)

        try:
            # Changer le répertoire de travail vers le répertoire du script
            script_dir = os.path.dirname(os.path.abspath(script_path))

            # Définir l'environnement avec l'encodage UTF-8
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=script_dir,
                env=env,
                encoding='utf-8',
                errors='replace'  # Remplace les caractères non décodables
            )

            # Lire la sortie ligne par ligne avec un délai pour éviter la récursion
            output_lines = []
            while True:
                line = process.stdout.readline()
                if line:
                    output_lines.append(line)
                    # Afficher par batch pour éviter trop d'appels à append_output
                    if len(output_lines) >= 5:  # Afficher par groupes de 5 lignes
                        self.append_output(''.join(output_lines))
                        output_lines = []
                elif process.poll() is not None:
                    break

            # Afficher les dernières lignes s'il y en a
            if output_lines:
                self.append_output(''.join(output_lines))

            process.wait()

            if process.returncode == 0:
                self.append_output("\n[SUCCES] Script exécuté avec succès!\n")
            else:
                self.append_output(f"\n[ERREUR] Script terminé avec le code d'erreur: {process.returncode}\n")

        except Exception as e:
            self.append_output(f"[ERREUR] Erreur lors de l'exécution: {str(e)}\n")

    def run_python_script_simple(self, script_path, args=None):
        """Exécute un script Python de manière simple (sans lecture en temps réel)"""
        cmd = [sys.executable, script_path]
        if args:
            cmd.extend(args)

        try:
            # Changer le répertoire de travail vers le répertoire du script
            script_dir = os.path.dirname(os.path.abspath(script_path))

            # Définir l'environnement avec l'encodage UTF-8
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # Exécuter et attendre la fin
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=script_dir,
                env=env,
                encoding='utf-8',
                errors='replace',
                timeout=300  # Timeout de 5 minutes
            )

            # Afficher toute la sortie d'un coup
            if result.stdout:
                self.append_output(result.stdout)
            if result.stderr:
                self.append_output(f"[STDERR] {result.stderr}")

            if result.returncode == 0:
                self.append_output("\n[SUCCES] Script exécuté avec succès!\n")
            else:
                self.append_output(f"\n[ERREUR] Script terminé avec le code d'erreur: {result.returncode}\n")

        except subprocess.TimeoutExpired:
            self.append_output("[ERREUR] Le script a dépassé le délai d'attente (5 minutes)\n")
        except Exception as e:
            self.append_output(f"[ERREUR] Erreur lors de l'exécution: {str(e)}\n")

    def run_python_script_with_log_window(self, script_path, args=None, script_type="Script"):
        """Exécute un script Python avec une fenêtre de logs séparée"""
        try:
            # Créer et ouvrir la fenêtre de logs
            log_window = LogWindow(self.root, title="Logs nnU-Net", script_type=script_type)

            # Démarrer le processus dans la fenêtre de logs
            log_window.start_process(script_path, args)

            self.append_output(f"� {script_type} lancé dans une fenêtre de logs séparée\n")
            self.append_output(f"� Script: {os.path.basename(script_path)}\n")
            self.append_output(f"�️ Consultez la fenêtre de logs pour suivre la progression en temps réel\n")

        except Exception as e:
            self.append_output(f"[ERREUR] Erreur lors du lancement de la fenêtre de logs: {str(e)}\n")
            # Fallback vers l'exécution normale
            self.append_output("[INFO] Basculement vers l'exécution normale...\n")
            self.run_python_script(script_path, args)

    def _run_check_shape(self):
        """Exécute nifti_check_shape.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NIfTI\n")
            return

        # Créer un script temporaire avec le bon chemin
        temp_script = self._create_temp_check_shape_script(file_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_check_shape_script(self, file_path):
        """Crée un script temporaire pour nifti_check_shape avec le bon chemin"""
        temp_script = "temp_check_shape.py"
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(f'''
import nibabel as nib

def check_nifti_shape(file_path):
    try:
        nifti_img = nib.load(file_path)
        shape = nifti_img.shape
        print(f"Chemin du fichier: {{file_path}}")
        print(f"Forme de l'image: {{shape}}")
        print(f"Nombre de dimensions: {{len(shape)}}")
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier: {{str(e)}}")

if __name__ == "__main__":
    file_path = r"{file_path}"
    check_nifti_shape(file_path)
''')
        return temp_script

    def _run_dataset_2d_to_3d(self):
        """Exécute conversion_dataset_2d_to_3d.py"""
        images_path = self.input_path.get()
        labels_path = self.labels_path.get()
        output_path = self.output_path.get()
        dataset_name = self.dataset_name.get()

        if not all([images_path, labels_path, output_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_dataset_2d_to_3d_script(
            images_path, labels_path, output_path, dataset_name)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_dataset_2d_to_3d_script(self, images_path, labels_path, output_path, dataset_name):
        """Crée un script temporaire pour la conversion dataset 2D vers 3D"""
        temp_script = "temp_dataset_2d_to_3d.py"

        # Lire le script original et remplacer les chemins
        with open("conversion_dataset_2d_to_3d.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple pour éviter les erreurs d'encodage
        content = content.replace('🎯', '[INFO]')
        content = content.replace('📂', '[DOSSIER]')
        content = content.replace('🏷️', '[LABELS]')
        content = content.replace('📤', '[OUTPUT]')
        content = content.replace('📋', '[DATASET]')
        content = content.replace('�', '[MODE]')
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('✅', '[OK]')
        content = content.replace('⚠️', '[ATTENTION]')
        content = content.replace('🔧', '[CONFIG]')
        content = content.replace('📦', '[VOLUMES]')
        content = content.replace('📄', '[FICHIER]')
        content = content.replace('🎉', '[SUCCES]')
        content = content.replace('💥', '[ECHEC]')
        content = content.replace('🔍', '[ANALYSE]')
        content = content.replace('📊', '[STATS]')
        content = content.replace('📏', '[FORME]')

        # Remplacer les chemins hardcodés
        content = content.replace(
            'IMAGES_ROOT = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset014_test4labeldifferentUint8\\imagesTr"',
            f'IMAGES_ROOT = r"{images_path}"'
        )
        content = content.replace(
            'LABELS_ROOT = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset014_test4labeldifferentUint8\\labelsTr"',
            f'LABELS_ROOT = r"{labels_path}"'
        )
        content = content.replace(
            'OUTPUT_DIR = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw"',
            f'OUTPUT_DIR = r"{output_path}"'
        )
        content = content.replace(
            'DATASET_NAME = "Dataset015_test4labeldifferentUint8_3d"',
            f'DATASET_NAME = "{dataset_name}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_inference_2d_to_3d(self):
        """Exécute conversion_inference_2d_to_3d.py"""
        images_path = self.input_path.get()
        output_path = self.output_path.get()

        if not all([images_path, output_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_inference_2d_to_3d_script(images_path, output_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_inference_2d_to_3d_script(self, images_path, output_path):
        """Crée un script temporaire pour la conversion inférence 2D vers 3D"""
        temp_script = "temp_inference_2d_to_3d.py"

        with open("conversion_inference_2d_to_3d.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('🔄', '[CONVERSION]')
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('✅', '[OK]')
        content = content.replace('🎯', '[INFO]')
        content = content.replace('📂', '[DOSSIER]')
        content = content.replace('📤', '[OUTPUT]')
        content = content.replace('🎉', '[SUCCES]')
        content = content.replace('💥', '[ECHEC]')
        content = content.replace('�', '[CONVERSION]')
        content = content.replace('�', '[ANALYSE]')
        content = content.replace('📊', '[STATS]')
        content = content.replace('📏', '[FORME]')

        content = content.replace(
            'IMAGES_ROOT = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\inference_test4labeldifferent"',
            f'IMAGES_ROOT = r"{images_path}"'
        )
        content = content.replace(
            'OUTPUT_IMAGESTS = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\inference_test4labeldifferent_3d"',
            f'OUTPUT_IMAGESTS = r"{output_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_transpose(self):
        """Exécute nifti_transpose.py"""
        input_path = self.input_path.get()
        output_path = self.output_path.get()

        if not all([input_path, output_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_transpose_script(input_path, output_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_transpose_script(self, input_path, output_path):
        """Crée un script temporaire pour nifti_transpose"""
        temp_script = "temp_transpose.py"
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(f'''
import nibabel as nib
import numpy as np

def transpose_nifti(input_path, output_path):
    try:
        nifti_img = nib.load(input_path)
        data = nifti_img.get_fdata()
        affine = nifti_img.affine
        transposed_data = np.transpose(data, (1, 2, 0))
        new_nifti = nib.Nifti1Image(transposed_data, affine)
        nib.save(new_nifti, output_path)
        print(f"Image originale - Forme: {{data.shape}}")
        print(f"Image transposée - Forme: {{transposed_data.shape}}")
        print(f"Image sauvegardée dans: {{output_path}}")
    except Exception as e:
        print(f"Erreur lors du traitement: {{str(e)}}")

if __name__ == "__main__":
    input_path = r"{input_path}"
    output_path = r"{output_path}"
    transpose_nifti(input_path, output_path)
''')
        return temp_script

    def _run_view_volume(self):
        """Exécute nifti_view_volume.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NIfTI\n")
            return

        temp_script = self._create_temp_view_volume_script(file_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_view_volume_script(self, file_path):
        """Crée un script temporaire pour nifti_view_volume"""
        temp_script = "temp_view_volume.py"
        with open("nifti_view_volume.py", 'r', encoding='utf-8') as f:
            content = f.read()

        content = content.replace(
            'path_to_seg = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Results\\inference\\inference_test4labeldifferent_3d_v3\\case_001.nii.gz"',
            f'path_to_seg = r"{file_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_npz_view_volume(self):
        """Exécute npz_view_volume.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NPZ\n")
            return

        # Afficher les paramètres
        self.append_output(f"🚀 Exécution de npz_view_volume.py...\n\n")
        self.append_output(f"📄 Fichier NPZ : {file_path}\n\n")

        # Exécuter le script avec le fichier en argument
        self.run_python_script("npz_view_volume.py", [file_path])

    def _run_npz_check_shape(self):
        """Exécute npz_check_shape.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NPZ\n")
            return

        # Afficher les paramètres
        self.append_output(f"🚀 Exécution de npz_check_shape.py...\n\n")
        self.append_output(f"📄 Fichier NPZ : {file_path}\n\n")

        # Exécuter le script
        self.run_python_script("npz_check_shape.py", [file_path])

    def _run_h5_check_shape(self):
        """Exécute h5_check_shape.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier H5\n")
            return

        # Afficher les paramètres
        self.append_output(f"🚀 Exécution de h5_check_shape.py...\n\n")
        self.append_output(f"📄 Fichier H5 : {file_path}\n\n")

        # Exécuter le script
        self.run_python_script("h5_check_shape.py", [file_path])

    def _run_nifti_export_png(self):
        """Exécute nifti_export_png.py"""
        input_file = self.file_path.get()
        output_dir = self.output_path.get()

        if not all([input_file, output_dir]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_nifti_export_png_script(input_file, output_dir)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_nifti_export_png_script(self, input_file, output_dir):
        """Crée un script temporaire pour nifti_export_png"""
        temp_script = "temp_nifti_export_png.py"

        # Récupérer les paramètres
        prefix = self.export_prefix.get() or "slice"
        dpi = self.export_dpi.get().strip()
        image_type = self.export_image_type.get() or "auto"
        start_slice = self.export_start_slice.get().strip()
        end_slice = self.export_end_slice.get().strip()
        uint8_mode = self.export_uint8_mode.get()

        # Déterminer le DPI une seule fois ici
        if dpi:
            final_dpi = int(dpi)
        else:
            # Résolution automatique basée sur les dimensions
            import nibabel as nib
            temp_volume = nib.load(input_file).get_fdata()
            avg_dim = (temp_volume.shape[1] + temp_volume.shape[2]) / 2
            if avg_dim > 512:
                final_dpi = 300
            elif avg_dim > 256:
                final_dpi = 200
            else:
                final_dpi = 150

        # Créer le contenu du script
        script_content = f'''
import sys
sys.path.append('.')
from nifti_export_png import NIfTIToPNGExporter

# Paramètres
input_file = r"{input_file}"
output_dir = r"{output_dir}"
prefix = "{prefix}"
image_type = "{image_type}"
export_uint8 = {uint8_mode}
dpi = {final_dpi}

try:
    # Créer l'exporteur
    exporter = NIfTIToPNGExporter(
        nifti_path=input_file,
        output_dir=output_dir,
        prefix=prefix,
        image_type=image_type,
        export_uint8=export_uint8
    )

    # Exporter selon les paramètres
'''

        if start_slice and end_slice:
            script_content += f'''
    start_slice = {start_slice}
    end_slice = {end_slice}
    exporter.export_slice_range(start_slice, end_slice, dpi=dpi)
'''
        else:
            script_content += f'''
    exporter.export_all_slices(dpi=dpi)
'''

        script_content += '''
except Exception as e:
    print("❌ Erreur lors de l'export :", str(e))
    import traceback
    traceback.print_exc()
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_npz_export_png(self):
        """Exécute npz_export_png.py"""
        input_file = self.file_path.get()
        output_dir = self.output_path.get()

        if not all([input_file, output_dir]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_npz_export_png_script(input_file, output_dir)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_npz_export_png_script(self, input_file, output_dir):
        """Crée un script temporaire pour npz_export_png"""
        temp_script = "temp_npz_export_png.py"

        # Récupérer les paramètres
        prefix = self.export_prefix.get() or "slice"
        dpi = self.export_dpi.get().strip()
        image_type = self.export_image_type.get() or "auto"
        start_slice = self.export_start_slice.get().strip()
        end_slice = self.export_end_slice.get().strip()
        uint8_mode = self.export_uint8_mode.get()

        # Déterminer le DPI une seule fois ici
        if dpi:
            final_dpi = int(dpi)
        else:
            # Résolution automatique basée sur les dimensions
            import numpy as np
            temp_npz = np.load(input_file)
            first_key = list(temp_npz.keys())[0]
            temp_volume = temp_npz[first_key]
            if temp_volume.ndim >= 2:
                avg_dim = (temp_volume.shape[-2] + temp_volume.shape[-1]) / 2
                if avg_dim > 512:
                    final_dpi = 300
                elif avg_dim > 256:
                    final_dpi = 200
                else:
                    final_dpi = 150
            else:
                final_dpi = 150

        # Créer le contenu du script
        script_content = f'''
import sys
sys.path.append('.')
from npz_export_png import NPZToPNGExporter

# Paramètres
input_file = r"{input_file}"
output_dir = r"{output_dir}"
prefix = "{prefix}"
image_type = "{image_type}"
export_uint8 = {uint8_mode}
dpi = {final_dpi}

try:
    # Créer l'exporteur
    exporter = NPZToPNGExporter(
        npz_path=input_file,
        output_dir=output_dir,
        prefix=prefix,
        image_type=image_type,
        export_uint8=export_uint8
    )

    # Exporter selon les paramètres
'''

        if start_slice and end_slice:
            script_content += f'''
    start_slice = {start_slice}
    end_slice = {end_slice}
    exporter.export_slice_range(start_slice, end_slice, dpi=dpi)
'''
        else:
            script_content += f'''
    exporter.export_all_slices(dpi=dpi)
'''

        script_content += '''
except Exception as e:
    print("❌ Erreur lors de l'export :", str(e))
    import traceback
    traceback.print_exc()
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_h5_view_volume(self):
        """Exécute h5_view_volume.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier H5\n")
            return

        temp_script = self._create_temp_h5_view_volume_script(file_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_h5_view_volume_script(self, file_path):
        """Crée un script temporaire pour h5_view_volume"""
        temp_script = "temp_h5_view_volume.py"

        # Récupérer le mode de colorisation
        colorize_mode = self.h5_colorize_mode.get() or "auto"

        # Créer le contenu du script
        script_content = f'''
import sys
sys.path.append('.')
from h5_view_volume import H5VolumeViewer

# Paramètres
path_to_h5 = r"{file_path}"
colorize_mode = "{colorize_mode}"
omniscan_colormap_path = './OmniScanColorMap.npy'

try:
    # Lancement du visualiseur
    viewer = H5VolumeViewer(path_to_h5, colorize_mode, omniscan_colormap_path)
except Exception as e:
    print(f"❌ Erreur lors du lancement du visualiseur : {{str(e)}}")
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_h5_export_png(self):
        """Exécute h5_export_png.py"""
        input_file = self.file_path.get()
        output_dir = self.output_path.get()

        if not all([input_file, output_dir]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_h5_export_png_script(input_file, output_dir)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_h5_export_png_script(self, input_file, output_dir):
        """Crée un script temporaire pour h5_export_png"""
        temp_script = "temp_h5_export_png.py"

        # Récupérer les paramètres
        prefix = self.export_prefix.get() or "slice"
        dpi = self.export_dpi.get().strip()
        image_type = self.export_image_type.get() or "auto"
        start_slice = self.export_start_slice.get().strip()
        end_slice = self.export_end_slice.get().strip()
        uint8_mode = self.export_uint8_mode.get()

        # Déterminer le DPI une seule fois ici
        if dpi:
            final_dpi = int(dpi)
        else:
            # Résolution automatique basée sur les dimensions
            import h5py
            with h5py.File(input_file, 'r') as h5_file:
                # Trouver le premier dataset pour estimer les dimensions
                def find_first_dataset(group, path=""):
                    for key in group.keys():
                        item = group[key]
                        current_path = f"{path}/{key}" if path else key
                        if isinstance(item, h5py.Group):
                            result = find_first_dataset(item, current_path)
                            if result:
                                return result
                        elif isinstance(item, h5py.Dataset) and len(item.shape) >= 2:
                            return current_path
                    return None

                first_dataset_path = find_first_dataset(h5_file)
                if first_dataset_path:
                    temp_volume = h5_file[first_dataset_path]
                    if temp_volume.ndim >= 2:
                        avg_dim = (temp_volume.shape[-2] + temp_volume.shape[-1]) / 2
                        if avg_dim > 512:
                            final_dpi = 300
                        elif avg_dim > 256:
                            final_dpi = 200
                        else:
                            final_dpi = 150
                    else:
                        final_dpi = 150
                else:
                    final_dpi = 150

        # Créer le contenu du script
        script_content = f'''
import sys
sys.path.append('.')
from h5_export_png import H5ToPNGExporter

# Paramètres
input_file = r"{input_file}"
output_dir = r"{output_dir}"
prefix = "{prefix}"
image_type = "{image_type}"
export_uint8 = {uint8_mode}
dpi = {final_dpi}

try:
    # Créer l'exporteur
    exporter = H5ToPNGExporter(
        h5_path=input_file,
        output_dir=output_dir,
        prefix=prefix,
        image_type=image_type,
        export_uint8=export_uint8
    )

    # Exporter selon les paramètres
'''

        if start_slice and end_slice:
            script_content += f'''
    start_slice = {start_slice}
    end_slice = {end_slice}
    exporter.export_slice_range(start_slice, end_slice, dpi=dpi)
'''
        else:
            script_content += f'''
    exporter.export_all_slices(dpi=dpi)
'''

        script_content += '''
except Exception as e:
    print("❌ Erreur lors de l'export :", str(e))
    import traceback
    traceback.print_exc()
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_nifti_export_dossier_png(self):
        """Exécute nifti_export_dossier_png.py"""
        input_folder = self.file_path.get()
        output_folder = self.output_path.get()

        if not all([input_folder, output_folder]):
            self.append_output("[ERREUR] Veuillez spécifier le dossier d'entrée et le dossier de sortie\n")
            return

        # Récupérer les paramètres
        prefix = self.export_prefix.get() or "slice"
        dpi = self.export_dpi.get().strip()
        image_type = self.export_image_type.get() or "auto"
        uint8_mode = self.export_uint8_mode.get()

        # Construire les arguments
        args = [input_folder, output_folder, "--prefix", prefix, "--image-type", image_type]

        if uint8_mode:
            args.append("--uint8")

        if dpi:
            args.extend(["--dpi", dpi])

        self.append_output(f"🚀 Exécution de nifti_export_dossier_png.py...\n\n")
        self.append_output(f"📂 Dossier d'entrée : {input_folder}\n")
        self.append_output(f"📁 Dossier de sortie : {output_folder}\n")
        self.append_output(f"🏷️ Type d'images : {image_type}\n")
        self.append_output(f"🎨 Mode export : {'uint8' if uint8_mode else 'RGB/Couleurs'}\n\n")

        self.run_python_script("nifti_export_dossier_png.py", args)

    def _run_npz_export_dossier_png(self):
        """Exécute npz_export_dossier_png.py"""
        input_folder = self.file_path.get()
        output_folder = self.output_path.get()

        if not all([input_folder, output_folder]):
            self.append_output("[ERREUR] Veuillez spécifier le dossier d'entrée et le dossier de sortie\n")
            return

        # Récupérer les paramètres
        prefix = self.export_prefix.get() or "slice"
        dpi = self.export_dpi.get().strip()
        image_type = self.export_image_type.get() or "auto"
        uint8_mode = self.export_uint8_mode.get()

        # Construire les arguments
        args = [input_folder, output_folder, "--prefix", prefix, "--image-type", image_type]

        if uint8_mode:
            args.append("--uint8")

        if dpi:
            args.extend(["--dpi", dpi])

        self.append_output(f"🚀 Exécution de npz_export_dossier_png.py...\n\n")
        self.append_output(f"📂 Dossier d'entrée : {input_folder}\n")
        self.append_output(f"📁 Dossier de sortie : {output_folder}\n")
        self.append_output(f"🏷️ Type d'images : {image_type}\n")
        self.append_output(f"🎨 Mode export : {'uint8' if uint8_mode else 'RGB/Couleurs'}\n\n")

        self.run_python_script("npz_export_dossier_png.py", args)

    def _run_h5_export_dossier_png(self):
        """Exécute h5_export_dossier_png.py"""
        input_folder = self.file_path.get()
        output_folder = self.output_path.get()

        if not all([input_folder, output_folder]):
            self.append_output("[ERREUR] Veuillez spécifier le dossier d'entrée et le dossier de sortie\n")
            return

        # Récupérer les paramètres
        prefix = self.export_prefix.get() or "slice"
        dpi = self.export_dpi.get().strip()
        image_type = self.export_image_type.get() or "auto"
        uint8_mode = self.export_uint8_mode.get()

        # Construire les arguments
        args = [input_folder, output_folder, "--prefix", prefix, "--image-type", image_type]

        if uint8_mode:
            args.append("--uint8")

        if dpi:
            args.extend(["--dpi", dpi])

        self.append_output(f"🚀 Exécution de h5_export_dossier_png.py...\n\n")
        self.append_output(f"📂 Dossier d'entrée : {input_folder}\n")
        self.append_output(f"📁 Dossier de sortie : {output_folder}\n")
        self.append_output(f"🏷️ Type d'images : {image_type}\n")
        self.append_output(f"🎨 Mode export : {'uint8' if uint8_mode else 'RGB/Couleurs OmniScan'}\n\n")

        self.run_python_script("h5_export_dossier_png.py", args)

    def _run_pngs_to_volume(self):
        """Exécute pngs_to_volume.py"""
        png_folder = self.png_folder_path.get()
        output_path = self.volume_output_path.get()

        if not all([png_folder, output_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        # Récupérer les paramètres
        format_type = self.volume_format.get()
        color_mode = self.volume_color_mode.get()
        axis_order = self.volume_axis_order.get()

        # Construire les arguments
        args = [
            png_folder,
            output_path,
            "--format", format_type,
            "--color-mode", color_mode,
            "--axis-order", axis_order
        ]

        # Afficher les paramètres
        self.append_output(f"🚀 Exécution de pngs_to_volume.py...\n\n")
        self.append_output(f"📂 Dossier PNG : {png_folder}\n")
        self.append_output(f"📁 Fichier sortie : {output_path}\n")
        self.append_output(f"🎨 Format : {format_type.upper()}\n")
        self.append_output(f"🖼️ Mode couleur : {color_mode}\n")
        self.append_output(f"📊 Ordre des axes : {axis_order}\n\n")

        self.run_python_script("pngs_to_volume.py", args)

    def _run_png_folder(self):
        """Exécute view_png_folder.py"""
        folder_path = self.input_path.get()
        max_images_str = self.max_images.get().strip()

        if not folder_path:
            self.append_output("[ERREUR] Veuillez sélectionner un dossier PNG\n")
            return

        # Traiter le nombre max d'images
        max_images = None
        if max_images_str:
            try:
                max_images = int(max_images_str)
            except ValueError:
                self.append_output("[ATTENTION] Nombre d'images invalide, toutes les images seront chargées\n")

        temp_script = self._create_temp_png_folder_script(folder_path, max_images)
        self.run_python_script_simple(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass

    def _create_temp_png_folder_script(self, folder_path, max_images):
        """Crée un script temporaire pour view_png_folder"""
        temp_script = "temp_png_folder.py"

        script_content = f'''
import sys
import os
sys.path.append(r"{os.getcwd()}")

from view_png_folder import PNGFolderViewer

try:
    print("Lancement du visualiseur PNG...")
    max_imgs = {max_images} if {max_images is not None} else None
    viewer = PNGFolderViewer(r"{folder_path}", max_images=max_imgs)
    print("Visualiseur fermé.")
except Exception as e:
    print(f"Erreur: {{e}}")
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_analyze_labels(self):
        """Exécute view_labels.py"""
        labels_dir = self.input_path.get()
        if not labels_dir:
            self.append_output("[ERREUR] Veuillez sélectionner un dossier de labels\n")
            return

        temp_script = self._create_temp_analyze_labels_script(labels_dir)
        # Utiliser la méthode simple pour éviter les problèmes de récursion
        self.run_python_script_simple(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass  # Ignorer si le fichier n'existe plus

    def _create_temp_analyze_labels_script(self, labels_dir):
        """Crée un script temporaire pour view_labels"""
        temp_script = "temp_analyze_labels.py"
        with open("view_labels.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('📁', '[DOSSIER]')

        content = content.replace(
            'labels_dir = Path(r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset012_test4labeldifferent\\labelsTr_backup")',
            f'labels_dir = Path(r"{labels_dir}")'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_visualize_mask(self):
        """Exécute view_mask.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier image\n")
            return

        temp_script = self._create_temp_visualize_mask_script(file_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_visualize_mask_script(self, file_path):
        """Crée un script temporaire pour view_mask"""
        temp_script = "temp_visualize_mask.py"
        with open("view_mask.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('→', '->')

        content = content.replace(
            'image_path = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset012_test4labeldifferent\\labelsTr_backup\\0001.png"',
            f'image_path = r"{file_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def show_help(self):
        """Affiche l'aide pour l'utilisation de l'interface"""
        help_text = """
🔬 AIDE - Interface de Traitement d'Images

📋 SCRIPTS DISPONIBLES:

🔍 Vérifier la forme d'un fichier NIfTI
   - Affiche les dimensions d'un fichier .nii.gz
   - Paramètres: Fichier NIfTI d'entrée

📦 Convertir dataset 2D vers 3D
   - Convertit des images PNG 2D en volumes 3D NIfTI pour nnU-Net
   - Paramètres: Dossier images, dossier labels, dossier sortie, nom dataset, slices par volume

🔄 Convertir inférence 2D vers 3D
   - Convertit des images PNG 2D en volumes 3D pour l'inférence
   - Paramètres: Dossier images, dossier sortie, slices par volume

🔀 Transposer un fichier NIfTI
   - Change l'orientation des axes d'un volume NIfTI
   - Paramètres: Fichier d'entrée, fichier de sortie

👁️ Visualiser un volume NIfTI
   - Affiche les slices d'un volume NIfTI avec colormap
   - Paramètres: Fichier NIfTI

🎨 Visualiser 10 masques
   - Affiche plusieurs masques PNG avec couleurs
   - Paramètres: Dossier masques, nombre d'images

📊 Analyser les labels
   - Analyse les valeurs uniques dans les images de labels
   - Paramètres: Dossier labels

🖼️ Visualiser un masque
   - Visualise un masque PNG avec normalisation
   - Paramètres: Fichier image

� Visualiser fichiers NDE
   - Visualise les fichiers NDE avec correction d'orientation
   - Paramètres: Fichier NDE

🚀 Entraîner modèle nnU-Net
   - Lance l'entraînement d'un modèle nnU-Net
   - Paramètres: Dataset ID, configuration, fold, epochs, GPU, chemins

🔮 Inférence nnU-Net
   - Effectue l'inférence avec un modèle nnU-Net entraîné
   - Paramètres: Dataset ID, configuration, dossiers d'entrée/sortie

📦 Exporter modèle ZIP
   - Exporte un modèle nnU-Net entraîné en fichier ZIP
   - Paramètres: Dataset ID, configuration, trainer, nom du fichier

�💡 UTILISATION:
1. Sélectionnez un script dans la liste déroulante
2. Remplissez les paramètres requis
3. Cliquez sur "Exécuter"
4. Consultez la sortie dans la zone de texte

⚠️ NOTES:
- Les chemins peuvent être saisis manuellement ou sélectionnés via les boutons
- L'exécution se fait en arrière-plan, l'interface reste responsive
- Les scripts temporaires sont automatiquement nettoyés après exécution
        """

        # Créer une nouvelle fenêtre pour l'aide
        help_window = tk.Toplevel(self.root)
        help_window.title("Aide")
        help_window.geometry("800x600")

        # Zone de texte avec scrollbar
        help_frame = ttk.Frame(help_window, padding="10")
        help_frame.pack(fill=tk.BOTH, expand=True)

        help_text_widget = scrolledtext.ScrolledText(help_frame, wrap=tk.WORD, font=('Consolas', 10))
        help_text_widget.pack(fill=tk.BOTH, expand=True)
        help_text_widget.insert(tk.END, help_text)
        help_text_widget.config(state=tk.DISABLED)

    def _run_view_nde(self):
        """Exécute view_nde_files.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NDE\n")
            return

        self.append_output("🔬 Ouverture du visualiseur NDE interactif...\n")
        self.append_output("📋 Contrôles disponibles :\n")
        self.append_output("  ← → (ou A/D) : Slice précédente/suivante\n")
        self.append_output("  ↑ ↓ (ou W/S) : Dataset précédent/suivant\n")
        self.append_output("  Home/End     : Première/dernière slice\n")
        self.append_output("  I            : Informations détaillées\n")
        self.append_output("  H            : Afficher l'aide\n")
        self.append_output("  Q/Escape     : Quitter\n")
        self.append_output("📊 Détection automatique des datasets d'amplitude (int16/uint16)\n")
        self.append_output("✅ Orientation corrigée et démarrage à la première slice\n")
        self.append_output("🔍 Fermez la fenêtre pour continuer\n\n")

        # Créer un script temporaire avec le visualiseur NDE
        temp_script = self._create_temp_view_nde_script(file_path)
        self.run_python_script_simple(temp_script)

        try:
            os.remove(temp_script)
        except:
            pass

    def _create_temp_view_nde_script(self, file_path):
        """Crée un script temporaire pour view_nde_files avec le bon chemin"""
        temp_script = "temp_view_nde.py"
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(f'''
import sys
import os
sys.path.append(r"{os.getcwd()}")

from nde_viewer import NDEViewerStable

try:
    print("Lancement du visualiseur NDE...")
    viewer = NDEViewerStable(r"{file_path}")
    viewer.show()
    print("Visualiseur NDE fermé.")
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
''')
        return temp_script

    def _run_copie_aleatoire(self):
        """Exécute copy_random_images.py"""
        self.append_output("🎲 Lancement du script de copie aléatoire...\n")
        self.run_python_script("copy_random_images.py")

    def _run_copie_correspondants(self):
        """Exécute copy_matching_files.py"""
        self.append_output("📋 Lancement du script de copie de fichiers correspondants...\n")
        self.run_python_script("copy_matching_files.py")

    def _run_copie_resolution(self):
        """Exécute copy_by_resolution.py"""
        self.append_output("📏 Lancement du script de copie par résolution...\n")
        self.run_python_script("copy_by_resolution.py")

    def _run_arborescence(self):
        """Exécute util_directory_tree.py"""
        folder_path = self.input_path.get()
        if not folder_path:
            self.append_output("[ERREUR] Veuillez sélectionner un dossier à analyser\n")
            return

        show_files = self.show_files_var.get()
        try:
            max_depth = int(self.max_depth_var.get())
        except ValueError:
            max_depth = 5
            self.append_output("[ATTENTION] Profondeur invalide, utilisation de la valeur par défaut (5)\n")

        temp_script = self._create_temp_arborescence_script(folder_path, show_files, max_depth)
        self.run_python_script_simple(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass

    def _create_temp_arborescence_script(self, folder_path, show_files, max_depth):
        """Crée un script temporaire pour util_directory_tree"""
        temp_script = "temp_arborescence.py"

        script_content = f'''
import os

def afficher_arborescence(dossier, prefixe='', niveau_max={max_depth}, niveau_actuel=0, afficher_fichiers={show_files}):
    if niveau_actuel > niveau_max:
        return ""

    contenu = []
    try:
        elements = sorted(os.listdir(dossier))
    except PermissionError:
        return f"{{prefixe}}[Permission Denied]\\n"

    for i, nom in enumerate(elements):
        chemin = os.path.join(dossier, nom)
        if not os.path.isdir(chemin) and not afficher_fichiers:
            continue

        dernier = (i == len(elements) - 1)
        branche = '└── ' if dernier else '├── '
        sous_prefixe = '    ' if dernier else '│   '
        ligne = f"{{prefixe}}{{branche}}{{nom}}\\n"
        contenu.append(ligne)

        if os.path.isdir(chemin):
            contenu.append(afficher_arborescence(chemin, prefixe + sous_prefixe, niveau_max, niveau_actuel + 1, afficher_fichiers))

    return ''.join(contenu)

def main():
    dossier_racine = r"{folder_path}"

    if not os.path.exists(dossier_racine):
        print(f"[ERREUR] Dossier introuvable : {{dossier_racine}}")
        return

    print(f"[INFO] Structure de : {{dossier_racine}}\\n")
    arbo = f"{{os.path.basename(dossier_racine)}}\\n"
    arbo += afficher_arborescence(dossier_racine, afficher_fichiers={show_files})

    print(arbo)

    with open("structure.txt", "w", encoding="utf-8") as f:
        f.write(arbo)
    print("\\n[SUCCES] Arborescence sauvegardée dans 'structure.txt'.")

if __name__ == "__main__":
    main()
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_overlay(self):
        """Exécute overlay.py"""
        image_dir = self.overlay_image_dir_var.get().strip()
        mask_dir = self.overlay_mask_dir_var.get().strip()
        output_dir = self.overlay_output_dir_var.get().strip()

        if not image_dir:
            self.append_output("❌ Veuillez sélectionner le dossier des images\n")
            return

        if not mask_dir:
            self.append_output("❌ Veuillez sélectionner le dossier des masques\n")
            return

        if not output_dir:
            self.append_output("❌ Veuillez sélectionner le dossier de sortie\n")
            return

        # Vérifier que les dossiers existent
        if not os.path.exists(image_dir):
            self.append_output(f"❌ Le dossier des images n'existe pas: {image_dir}\n")
            return

        if not os.path.exists(mask_dir):
            self.append_output(f"❌ Le dossier des masques n'existe pas: {mask_dir}\n")
            return

        # Créer le dossier de sortie s'il n'existe pas
        try:
            os.makedirs(output_dir, exist_ok=True)
        except Exception as e:
            self.append_output(f"❌ Impossible de créer le dossier de sortie: {str(e)}\n")
            return

        # Créer un script temporaire avec les chemins configurés
        temp_script = self._create_temp_overlay_script(image_dir, mask_dir, output_dir)

        self.append_output(f"🚀 Création des overlays...\n")
        self.append_output(f"📂 Images: {image_dir}\n")
        self.append_output(f"🎭 Masques: {mask_dir}\n")
        self.append_output(f"💾 Sortie: {output_dir}\n\n")

        self.run_python_script(temp_script)

        try:
            os.remove(temp_script)
        except Exception:
            pass

    def _create_temp_overlay_script(self, image_dir, mask_dir, output_dir):
        """Crée un script temporaire pour overlay avec les chemins configurés"""
        temp_script = "temp_overlay.py"

        script_content = f'''
import cv2
import numpy as np
import os
from pathlib import Path

# === Configuration des chemins ===
IMAGE_DIR = r"{image_dir}"
MASK_DIR = r"{mask_dir}"
OUT_DIR = r"{output_dir}"

def main():
    """Fonction principale"""
    # Créer le dossier de sortie
    os.makedirs(OUT_DIR, exist_ok=True)

    # Lister les fichiers images
    image_files = []
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        image_files.extend(Path(IMAGE_DIR).glob(ext))

    if not image_files:
        print("[⚠️] Aucune image trouvée dans le dossier")
        return

    print(f"[📊] {{len(image_files)}} images trouvées")

    for image_path in image_files:
        filename = image_path.stem
        mask_path = Path(MASK_DIR) / f"{{filename}}.png"

        if not mask_path.exists():
            print(f"[⚠️] Masque manquant pour {{filename}}")
            continue

        # Lire image et masque
        image = cv2.imread(str(image_path))
        mask_original = cv2.imread(str(mask_path), cv2.IMREAD_UNCHANGED)

        if image is None or mask_original is None:
            print(f"[⚠️] Fichier invalide : {{filename}}")
            continue

        # Créer l'overlay en utilisant directement les couleurs du masque original
        overlay = image.copy()

        # Si le masque est en couleur (RGB), l'utiliser directement
        if mask_original.ndim == 3:
            # Masque couleur - utiliser les couleurs exactes
            mask_color = mask_original
            # Créer un masque binaire pour identifier les zones non-noires (fond)
            mask_non_black = np.any(mask_color != [0, 0, 0], axis=2)

            print(f"[🔧] {{filename}} → masque couleur détecté, pixels non-noirs : {{np.sum(mask_non_black)}}")

            # Appliquer les couleurs exactes du masque sur l'image
            alpha = 0.6  # Transparence
            overlay[mask_non_black] = (alpha * mask_color[mask_non_black] + (1-alpha) * image[mask_non_black]).astype(np.uint8)

        else:
            # Masque en niveaux de gris - convertir en couleur en préservant les valeurs
            unique_vals = sorted([v for v in np.unique(mask_original) if v != 0])
            print(f"[🔧] {{filename}} → masque niveaux de gris, valeurs : {{unique_vals}}")

            for val in unique_vals:
                mask_area = (mask_original == val)
                if np.sum(mask_area) > 0:
                    # Convertir la valeur de gris en couleur (garder la même intensité)
                    color = [val, val, val]  # Couleur grise correspondante
                    alpha = 0.6
                    overlay[mask_area] = (alpha * np.array(color) + (1-alpha) * image[mask_area]).astype(np.uint8)

        # Sauvegarder l'overlay
        output_path = Path(OUT_DIR) / f"{{filename}}.png"
        cv2.imwrite(str(output_path), overlay)
        print(f"[✔️] Overlay créé : {{filename}}.png")

    print("✅ Tous les overlays ont été générés.")

if __name__ == "__main__":
    main()
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_h5py_nde_to_images(self):
        """Exécute H5py_NdeToImages.py"""
        files_str = self.h5py_files_var.get().strip()
        output_path = self.h5py_output_var.get().strip()

        if not files_str:
            self.append_output("❌ Veuillez sélectionner au moins un fichier NDE\n")
            return

        if not output_path:
            self.append_output("❌ Veuillez sélectionner un dossier de sortie\n")
            return

        # Séparer les fichiers
        files_list = [f.strip() for f in files_str.split(',') if f.strip()]

        # Créer les arguments
        args = ['--files'] + files_list + ['--output', output_path]

        self.append_output(f"🚀 Conversion de {len(files_list)} fichier(s) NDE vers images...\n")
        self.append_output(f"📂 Dossier de sortie: {output_path}\n\n")

        self.run_python_script_with_log_window("H5py_NdeToImages.py", args, "Conversion NDE vers Images")

    def _run_h5py_compare_file(self):
        """Exécute H5py_compareFile.py"""
        file1 = self.h5py_file1_var.get().strip()
        file2 = self.h5py_file2_var.get().strip()
        output_dir = self.h5py_output_dir_var.get().strip()

        if not file1:
            self.append_output("❌ Veuillez sélectionner le premier fichier NDE\n")
            return

        if not file2:
            self.append_output("❌ Veuillez sélectionner le second fichier NDE\n")
            return

        if not output_dir:
            output_dir = "./"

        # Créer les arguments
        args = ['--file1', file1, '--file2', file2, '--output', output_dir]

        self.append_output("🔍 Comparaison de fichiers NDE...\n")
        self.append_output(f"📁 Fichier 1: {file1}\n")
        self.append_output(f"📁 Fichier 2: {file2}\n")
        self.append_output(f"📂 Sortie: {output_dir}\n\n")

        self.run_python_script("H5py_compareFile.py", args)



    def _run_h5py_enlever_dossier(self):
        """Exécute H5py_enlever_dossier_inutile.py"""
        root_dir = self.h5py_root_dir_var.get().strip()

        if not root_dir:
            self.append_output("❌ Veuillez sélectionner le dossier racine des images\n")
            return

        # Créer les arguments
        args = ['--root_dir', root_dir]

        self.append_output("🧹 Nettoyage de la structure des dossiers...\n")
        self.append_output(f"📂 Dossier racine: {root_dir}\n")
        self.append_output("📂 Déplacement du contenu des dossiers 'no_label'\n")
        self.append_output("🗑️ Suppression des dossiers 'complete'\n\n")

        self.run_python_script("H5py_enlever_dossier_inutile.py", args)

    def _run_h5py_inversion(self):
        """Exécute H5py_inversion.py"""
        inversion_dir = self.h5py_inversion_dir_var.get().strip()

        if not inversion_dir:
            self.append_output("❌ Veuillez sélectionner le dossier racine des images\n")
            return

        # Créer les arguments
        args = ['--root_dir', inversion_dir, '--auto-confirm']

        self.append_output("🔄 Inversion des couleurs des images uint8...\n")
        self.append_output(f"📂 Dossier racine: {inversion_dir}\n")
        self.append_output("💾 Création automatique de backups\n")
        self.append_output("🎨 Traitement: 255 - valeur_pixel\n\n")

        self.run_python_script("H5py_inversion.py", args)

    def _run_h5py_open_file(self):
        """Exécute H5py_openFile.py"""
        nde_file = self.h5py_open_file_var.get().strip()

        if not nde_file:
            self.append_output("❌ Veuillez sélectionner un fichier NDE à explorer\n")
            return

        # Créer les arguments
        args = ['--file', nde_file]

        self.append_output("📖 Exploration de la structure du fichier NDE...\n")
        self.append_output(f"📁 Fichier: {nde_file}\n")
        self.append_output("🔍 Affichage des groupes, datasets et métadonnées\n\n")

        self.run_python_script("H5py_openFile.py", args)

    def _run_h5py_process_nde(self):
        """Exécute H5py_process_nde.py"""
        source_dir = self.h5py_source_dir_var.get().strip()
        dest_dir = self.h5py_dest_dir_var.get().strip()
        preserve_folders = self.h5py_preserve_folders_var.get()
        confirm_execution = self.h5py_confirm_execution_var.get()

        if not source_dir:
            self.append_output("❌ Veuillez sélectionner le dossier source (fichiers NDE)\n")
            return

        if not dest_dir:
            self.append_output("❌ Veuillez sélectionner le dossier destination (images)\n")
            return

        # Demander confirmation si l'option est activée
        if confirm_execution:
            # Analyser les fichiers avant de demander confirmation
            analysis_result = self._analyze_nde_files_for_confirmation(source_dir, dest_dir, preserve_folders)

            if analysis_result is None:
                self.append_output("❌ Erreur lors de l'analyse des fichiers\n")
                return

            if not self._show_confirmation_dialog(source_dir, dest_dir, preserve_folders, analysis_result):
                self.append_output("❌ Traitement annulé par l'utilisateur\n")
                return

        # Créer les arguments
        args = ['--source', source_dir, '--destination', dest_dir, '--auto-confirm']

        # Ajouter l'option de préservation des dossiers parents si activée
        if preserve_folders:
            args.append('--preserve-parent-folders')

        self.append_output("⚙️ Traitement automatique des fichiers NDE...\n")
        self.append_output(f"📂 Source: {source_dir}\n")
        self.append_output(f"📂 Destination: {dest_dir}\n")
        self.append_output(f"📁 Préserver dossiers parents: {'Oui' if preserve_folders else 'Non'}\n")
        self.append_output("🔍 Recherche récursive des fichiers .nde\n")
        self.append_output("🚫 Évite de retraiter les fichiers déjà traités\n\n")

        self.run_python_script_with_log_window("H5py_process_nde.py", args, "Traitement automatique NDE")

    def _analyze_nde_files_for_confirmation(self, source_dir, dest_dir, preserve_folders):
        """Analyse les fichiers NDE pour la confirmation - reproduit la logique de H5py_process_nde.py"""
        try:
            import re
            import unicodedata

            # Fonction de normalisation (copiée de H5py_process_nde.py)
            def normalize_name(name):
                name = os.path.splitext(name)[0]
                name = unicodedata.normalize('NFD', name).encode('ascii', 'ignore').decode('utf-8')
                name = re.sub(r'\W+', '', name)
                return name.lower().strip()

            # Fonction pour trouver récursivement tous les fichiers .nde
            def find_nde_files(directory):
                nde_files = []
                for root, _, files in os.walk(directory):
                    for file in files:
                        if file.lower().endswith('.nde'):
                            full_path = os.path.join(root, file)
                            relative_path = os.path.relpath(full_path, source_dir)
                            nde_files.append((full_path, relative_path))
                return nde_files

            # Liste des fichiers .nde
            nde_files = find_nde_files(source_dir)

            # Fonction pour trouver récursivement tous les dossiers de destination existants
            def find_existing_output_dirs(base_folder):
                """Trouve récursivement tous les dossiers dans le dossier de destination"""
                existing_dirs_normalized = {}
                if not os.path.exists(base_folder):
                    return existing_dirs_normalized

                for root, dirs, files in os.walk(base_folder):
                    for dir_name in dirs:
                        full_path = os.path.join(root, dir_name)
                        normalized = normalize_name(dir_name)
                        # Stocker le chemin complet pour chaque nom normalisé
                        if normalized not in existing_dirs_normalized:
                            existing_dirs_normalized[normalized] = []
                        existing_dirs_normalized[normalized].append(full_path)

                return existing_dirs_normalized

            # Liste des dossiers déjà présents dans dest_dir (récursivement)
            existing_dirs_normalized = find_existing_output_dirs(dest_dir)

            # Vérification
            files_to_process = []
            files_skipped = []

            for nde_path, relative_path in nde_files:
                original_name = os.path.splitext(os.path.basename(relative_path))[0]
                normalized = normalize_name(original_name)

                # Construire le chemin de sortie
                if preserve_folders:
                    relative_dir = os.path.dirname(relative_path)
                    if relative_dir:
                        output_subfolder = os.path.join(dest_dir, relative_dir, original_name)
                    else:
                        output_subfolder = os.path.join(dest_dir, original_name)
                else:
                    output_subfolder = os.path.join(dest_dir, original_name)

                # Vérifier si un dossier avec ce nom normalisé existe déjà (récursivement)
                if normalized in existing_dirs_normalized:
                    # Trouver le dossier existant le plus pertinent
                    existing_paths = existing_dirs_normalized[normalized]
                    found_existing = None

                    # Chercher d'abord un dossier qui correspond exactement au chemin attendu
                    for existing_path in existing_paths:
                        try:
                            if os.path.exists(os.path.dirname(output_subfolder)) and os.path.samefile(os.path.dirname(existing_path), os.path.dirname(output_subfolder)):
                                found_existing = existing_path
                                break
                        except (OSError, FileNotFoundError):
                            # Ignorer les erreurs de comparaison de fichiers
                            pass

                    # Si pas de correspondance exacte, prendre le premier trouvé
                    if not found_existing:
                        found_existing = existing_paths[0]

                    files_skipped.append((relative_path, found_existing))
                else:
                    files_to_process.append((relative_path, output_subfolder))

            return {
                'total_files': len(nde_files),
                'files_to_process': files_to_process,
                'files_skipped': files_skipped,
                'preserve_folders': preserve_folders
            }

        except Exception as e:
            self.append_output(f"[ERREUR] Erreur lors de l'analyse: {str(e)}\n")
            return None

    def _show_confirmation_dialog(self, source_dir, dest_dir, preserve_folders, analysis_result):
        """Affiche une boîte de dialogue de confirmation détaillée"""
        import tkinter as tk
        from tkinter import ttk

        # Créer une nouvelle fenêtre
        dialog = tk.Toplevel(self.root)
        dialog.title("Confirmation d'exécution - H5py_process_nde")
        dialog.geometry("800x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Centrer la fenêtre
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"800x600+{x}+{y}")

        # Variable pour le résultat
        result = {'confirmed': False}

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        title_label = ttk.Label(main_frame, text="🔍 Analyse des fichiers NDE",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # Informations générales
        info_frame = ttk.LabelFrame(main_frame, text="Paramètres", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text=f"📂 Source: {source_dir}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"📂 Destination: {dest_dir}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"📁 Préserver dossiers parents: {'Oui' if preserve_folders else 'Non'}").pack(anchor=tk.W)

        # Résumé
        summary_frame = ttk.LabelFrame(main_frame, text="Résumé", padding="10")
        summary_frame.pack(fill=tk.X, pady=(0, 10))

        total_files = analysis_result['total_files']
        files_to_process = len(analysis_result['files_to_process'])
        files_skipped = len(analysis_result['files_skipped'])

        ttk.Label(summary_frame, text=f"📊 Total fichiers .nde trouvés: {total_files}",
                 font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        ttk.Label(summary_frame, text=f"🆕 Nouveaux fichiers à traiter: {files_to_process}",
                 font=('Arial', 10, 'bold'), foreground='green').pack(anchor=tk.W)
        ttk.Label(summary_frame, text=f"✅ Fichiers déjà traités (ignorés): {files_skipped}",
                 font=('Arial', 10, 'bold'), foreground='blue').pack(anchor=tk.W)

        # Notebook pour les onglets
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Onglet "Fichiers à traiter"
        if files_to_process > 0:
            process_frame = ttk.Frame(notebook)
            notebook.add(process_frame, text=f"🆕 À traiter ({files_to_process})")

            process_text = tk.Text(process_frame, wrap=tk.WORD, font=('Consolas', 9))
            process_scrollbar = ttk.Scrollbar(process_frame, orient=tk.VERTICAL, command=process_text.yview)
            process_text.configure(yscrollcommand=process_scrollbar.set)

            process_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            process_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            for i, (relative_path, output_path) in enumerate(analysis_result['files_to_process'], 1):
                process_text.insert(tk.END, f"{i:3d}. {relative_path}\n")
                process_text.insert(tk.END, f"     → {output_path}\n\n")

            process_text.config(state=tk.DISABLED)

        # Onglet "Fichiers déjà traités"
        if files_skipped > 0:
            skip_frame = ttk.Frame(notebook)
            notebook.add(skip_frame, text=f"✅ Déjà traités ({files_skipped})")

            skip_text = tk.Text(skip_frame, wrap=tk.WORD, font=('Consolas', 9))
            skip_scrollbar = ttk.Scrollbar(skip_frame, orient=tk.VERTICAL, command=skip_text.yview)
            skip_text.configure(yscrollcommand=skip_scrollbar.set)

            skip_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            skip_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            for i, (relative_path, existing_path) in enumerate(analysis_result['files_skipped'], 1):
                skip_text.insert(tk.END, f"{i:3d}. {relative_path}\n")
                skip_text.insert(tk.END, f"     ✅ {existing_path}\n\n")

            skip_text.config(state=tk.DISABLED)

        # Boutons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def on_confirm():
            result['confirmed'] = True
            dialog.destroy()

        def on_cancel():
            result['confirmed'] = False
            dialog.destroy()

        if files_to_process > 0:
            confirm_btn = ttk.Button(button_frame, text=f"🚀 Traiter {files_to_process} fichier(s)",
                                   command=on_confirm)
            confirm_btn.pack(side=tk.LEFT, padx=(0, 5))
            # Mettre le focus sur le bouton de confirmation
            confirm_btn.focus_set()
        else:
            ttk.Label(button_frame, text="ℹ️ Aucun nouveau fichier à traiter",
                     font=('Arial', 10, 'bold')).pack(side=tk.LEFT)

        ttk.Button(button_frame, text="❌ Annuler", command=on_cancel).pack(side=tk.RIGHT)

        # Raccourcis clavier
        def on_key_press(event):
            if event.keysym == 'Return' and files_to_process > 0:
                on_confirm()
            elif event.keysym == 'Escape':
                on_cancel()

        dialog.bind('<Key>', on_key_press)
        dialog.focus_set()

        # Attendre la fermeture de la fenêtre
        dialog.wait_window()

        return result['confirmed'] and files_to_process > 0


def main():
    """Fonction principale"""
    root = tk.Tk()
    app = ScriptGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
